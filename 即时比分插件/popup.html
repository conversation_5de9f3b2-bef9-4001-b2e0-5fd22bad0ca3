<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 100%;
      height: 100vh;
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      box-sizing: border-box;
    }

    #tableContainer {
      width: 100%;
      height: 400px;
      border: 1px solid #ccc;
      overflow: auto;
      margin-top: 10px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
    }

    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }

    th {
      background-color: #f2f2f2;
      font-weight: bold;
      position: sticky;
      top: 0;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    tr:hover {
      background-color: #f5f5f5;
    }

    .note-input {
      width: 100%;
      border: none;
      padding: 4px;
      background: transparent;
      font-size: 12px;
    }

    .note-input:focus {
      background-color: #fff;
      border: 1px solid #007cba;
      outline: none;
    }

    .status-checkbox {
      cursor: pointer;
    }

    th:first-child, td:first-child {
      width: 80px;
      text-align: center;
    }

    #selectAllCheckbox {
      cursor: pointer;
      margin-right: 5px;
    }

    .tab-button {
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .tab-button:hover {
      color: #007bff !important;
      background-color: #f8f9fa;
    }

    .tab-button.active {
      color: #007bff !important;
      border-bottom-color: #007bff !important;
      font-weight: bold;
    }

    .tab-content {
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    .tab-operation-btn {
      transition: all 0.3s ease;
    }

    .tab-operation-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .tab-operation-btn.active {
      background-color: #007bff !important;
      box-shadow: 0 2px 6px rgba(0,123,255,0.3);
    }
    
    #getSourceBtn {
      background-color: #4CAF50;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      width: 100%;
      margin-bottom: 10px;
    }
    
    #getSourceBtn:hover {
      background-color: #45a049;
    }
    
    #sourceCode {
      width: 100%;
      height: 350px;
      border: 1px solid #ccc;
      padding: 10px;
      font-family: monospace;
      font-size: 12px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
      box-sizing: border-box;
      resize: vertical;
    }
    
    .title {
      text-align: center;
      margin-bottom: 15px;
      color: #333;
    }
  </style>
</head>
<body>
  <!-- 标签页导航 -->
  <div style="margin-bottom: 15px;">
    <div style="border-bottom: 1px solid #ddd;">
      <button id="tab1" class="tab-button active" style="background: none; border: none; padding: 8px 12px; cursor: pointer; border-bottom: 2px solid #007bff; color: #007bff; font-size: 12px;">联赛管理</button>
      <button id="tab2" class="tab-button" style="background: none; border: none; padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; color: #666; font-size: 12px;">事件日志</button>
      <button id="tab3" class="tab-button" style="background: none; border: none; padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; color: #666; font-size: 12px;">套利计算器</button>
    </div>
  </div>

  <!-- 标签页内容 -->
  <div id="tabContent1" class="tab-content">
    <div style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
      <h5 style="margin: 0 0 8px 0; color: #333; font-size: 12px;">📋 标签页操作</h5>
      <div style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
        <button id="tabBtn1" class="tab-operation-btn active" style="background-color: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 11px;">联赛管理</button>
        <button id="tabBtn2" class="tab-operation-btn" style="background-color: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 11px;">事件日志</button>
        <button id="tabBtn3" class="tab-operation-btn" style="background-color: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 11px;">套利计算器</button>
        <span style="font-size: 11px; color: #666; margin-left: 10px;">当前操作：<span id="currentTabName" style="color: #007bff; font-weight: bold;">联赛管理</span></span>
      </div>
    </div>

    <div style="margin-bottom: 15px;">
      <button id="getSourceBtn">解析源码中的联赛名称</button>
      <input type="file" id="fileInput" accept=".json" style="display: none;">
    </div>
    <div id="tableContainer">
      <div id="loadingMsg" style="text-align: center; padding: 20px; color: #666;">
        点击按钮解析源码中的联赛名称...
      </div>
    </div>
    <div style="margin-top: 15px; text-align: center;">
      <button id="exportBtn" style="background-color: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 10px;">导出数据</button>
      <button id="importBtn" style="background-color: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 10px;">导入数据</button>
      <button id="selectLeaguesBtn" style="background-color: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">选中网页联赛</button>
    </div>
  </div>

  <div id="tabContent2" class="tab-content" style="display: none;">
    <div id="logContainer" style="height: 300px; border: 1px solid #ccc; overflow-y: auto; padding: 10px; background-color: #f9f9f9; font-size: 12px;">
    </div>
    <div style="margin-top: 10px;">
      <button id="toggleMonitorBtn" style="background-color: #28a745; color: white; border: none; padding: 5px 15px; border-radius: 3px; cursor: pointer;">开始监控</button>
      <button id="clearLogBtn" style="background-color: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 10px;">清空日志</button>
    </div>
    <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #ddd;">
      <div style="display: flex; align-items: center; margin-bottom: 10px;">
        <button id="soundToggle" style="background: none; border: none; font-size: 16px; cursor: pointer; margin-right: 10px;" title="点击切换提示音">🔊</button>
        <label style="font-size: 12px;">
          比赛进行到 <input type="number" id="timeLimit" value="80" min="0" max="999" style="width: 50px; font-size: 12px;"> 分钟后不提示
        </label>
      </div>
      <div style="display: flex; align-items: center; font-size: 12px;">
        <span style="margin-right: 10px;">提示音类型：</span>
        <select id="soundType" style="font-size: 12px; padding: 2px 5px; border: 1px solid #ccc; border-radius: 3px;">
          <option value="beep">哔哔声</option>
          <option value="ding">叮咚声</option>
          <option value="chime">钟声</option>
          <option value="notification">通知音</option>
          <option value="alert">警报音</option>
        </select>
        <button id="testSoundBtn" style="background-color: #6c757d; color: white; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer; font-size: 11px; margin-left: 10px;">试听</button>
      </div>
    </div>
  </div>

  <div id="tabContent3" class="tab-content" style="display: none;">
    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
      <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">两平台对冲套利计算器</h4>
      <p style="margin: 0; font-size: 11px; color: #666;">输入两个平台的赔率和投注金额，计算对冲套利的盈亏情况</p>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
      <div style="border: 1px solid #ddd; border-radius: 5px; padding: 15px; background-color: #fff;">
        <h5 style="margin: 0 0 10px 0; color: #007bff; font-size: 13px;">🅰️ A平台</h5>
        <div style="margin-bottom: 10px;">
          <label style="font-size: 12px; font-weight: bold; color: #333;">赔率：</label>
          <input type="number" id="oddsA" step="0.01" min="1" placeholder="2.50" style="width: 80px; padding: 5px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px; margin-left: 5px;">
        </div>
        <div>
          <label style="font-size: 12px; font-weight: bold; color: #333;">投注金额：</label>
          <input type="number" id="betA" step="1" min="1" placeholder="1000" style="width: 80px; padding: 5px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px; margin-left: 5px;">
          <span style="font-size: 11px; color: #666;">元</span>
        </div>
      </div>

      <div style="border: 1px solid #ddd; border-radius: 5px; padding: 15px; background-color: #fff;">
        <h5 style="margin: 0 0 10px 0; color: #28a745; font-size: 13px;">🅱️ B平台</h5>
        <div style="margin-bottom: 10px;">
          <label style="font-size: 12px; font-weight: bold; color: #333;">赔率：</label>
          <input type="number" id="oddsB" step="0.01" min="1" placeholder="1.80" style="width: 80px; padding: 5px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px; margin-left: 5px;">
        </div>
        <div>
          <label style="font-size: 12px; font-weight: bold; color: #333;">投注金额：</label>
          <input type="number" id="betB" step="1" min="1" placeholder="自动计算" readonly style="width: 80px; padding: 5px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px; margin-left: 5px; background-color: #f8f9fa;">
          <span style="font-size: 11px; color: #666;">元</span>
        </div>
      </div>
    </div>

    <div style="text-align: center; margin-bottom: 15px;">
      <button id="calculateBtn" style="background-color: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; cursor: pointer; font-size: 13px;">计算对冲套利</button>
    </div>

    <div id="arbitrageResult" style="background-color: #fff; border: 1px solid #ddd; border-radius: 5px; padding: 15px; font-size: 12px;">
      <div style="text-align: center; color: #666; padding: 20px;">
        输入A平台的赔率和投注金额，B平台的赔率，点击计算查看结果
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
