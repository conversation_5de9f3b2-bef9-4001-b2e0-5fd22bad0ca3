<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>WebSocket连接</title>
  <!-- 引入CryptoJS库用于加密解密 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>

  <!-- 预定义函数，确保HTML中的onclick可以调用 -->
  <script>
    // 当前激活的选项卡
    let activeTab = 'fulltime-handicap';

    // 切换选项卡函数
    function switchMatchTab(tabId) {
      // 更新当前激活的选项卡
      activeTab = tabId;

      // 更新选项卡样式
      document.querySelectorAll('.match-tab').forEach(tab => {
        tab.classList.remove('active');
        tab.style.backgroundColor = '#f0f0f0';
      });
      document.getElementById(`tab-${tabId}`).classList.add('active');

      // 根据选项卡ID设置不同的背景色
      switch(tabId) {
        case 'fulltime-handicap':
          document.getElementById(`tab-${tabId}`).style.backgroundColor = '#e6f2ff';
          break;
        case 'fulltime-overunder':
          document.getElementById(`tab-${tabId}`).style.backgroundColor = '#e6ffe6';
          break;
        case 'halftime-handicap':
          document.getElementById(`tab-${tabId}`).style.backgroundColor = '#fff2e6';
          break;
        case 'halftime-overunder':
          document.getElementById(`tab-${tabId}`).style.backgroundColor = '#f9e6ff';
          break;
        default:
          document.getElementById(`tab-${tabId}`).style.backgroundColor = '#f8f8f8';
      }

      // 隐藏所有内容
      document.querySelectorAll('.match-content').forEach(content => {
        content.style.display = 'none';
      });

      // 显示选中的内容
      document.getElementById(`content-${tabId}`).style.display = 'block';

      // 如果已有数据，重新应用过滤
      if (typeof fullMatchResults !== 'undefined' && fullMatchResults && fullMatchResults.length > 0) {
        // 过滤符合条件的比赛
        const filteredResults = fullMatchResults.filter(match => {
          const league = match.hg.league || '';
          return typeof availableLeagues !== 'undefined' && availableLeagues[league] === true;
        });

        // 更新表格显示
        if (typeof updateMatchTableUI !== 'undefined') {
          updateMatchTableUI(filteredResults);
        }
      }
    }
  </script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      padding: 0;
      background-color: #f4f4f4;
      margin: 0;
      font-size: 13px;
      -webkit-text-size-adjust: 100%;
    }
    
    h1 {
      font-size: 16px;
      margin: 6px 0;
    }
    
    .ws-status {
      display: inline-flex;
      align-items: center;
      font-size: 11px;
      margin: 8px 0;
    }
    .ws-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 3px;
      display: inline-block;
    }
    .ws-online {
      background-color: #17a2b8;
      box-shadow: 0 0 0 2px rgba(23, 162, 184, 0.2);
      animation: pulsate 2s infinite;
    }
    .ws-offline {
      background-color: #ffc107;
      box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
    }
    @keyframes pulsate {
      0% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.8; transform: scale(1.1); }
      100% { opacity: 1; transform: scale(1); }
    }
    
    /* 状态区域移动端优化 */
    .status-container {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      margin-bottom: 8px;
    }
    
    .status-item {
      display: flex;
      align-items: center;
      font-size: 11px;
      margin-right: 8px;
      margin-bottom: 4px;
    }
    
    /* 表格容器样式 */
    .table-container {
      max-height: 300px; /* 设置最大高度 */
      overflow-y: auto; /* 启用垂直滚动条 */
      margin-top: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 0;
      -webkit-overflow-scrolling: touch; /* iOS滚动优化 */
    }
    
    /* 表格样式 */
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 0; /* 取消表格上边距，因为容器已有边距 */
      background-color: white;
      font-size: 13px;
      font-weight: normal;
    }
    th, td {
      padding: 4px 3px;
      text-align: left;
      border-bottom: 1px solid #ddd;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100px;
      font-weight: normal;
    }
    th {
      background-color: #f2f2f2;
      font-weight: normal;
      position: sticky; /* 表头固定 */
      top: 0; /* 表头固定在顶部 */
      z-index: 10; /* 确保表头显示在数据上方 */
      box-shadow: 0 1px 0 #ddd; /* 给表头添加底部阴影 */
      font-size: 12px;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    /* HG和OB数据分隔样式 */
    .separator {
      width: 2px;
      background-color: #e0e0e0;
      padding: 0;
    }
    .hg-section {
      background-color: #f8f8ff;
    }
    .ob-section {
      background-color: #fffaf0;
    }
    
    /* 水位列样式 */
    .water-section {
      background-color: #f0f8ff; /* 浅蓝色背景 */
    }
    
    /* 水位提示文本样式 */
    .odds-text {
      white-space: pre-line; /* 使\n换行符生效 */
      line-height: 1.3; /* 行间距调整 */
      display: block; /* 块级显示 */
    }
    
    /* 盘口类型标题样式 */
    .odds-title {
      font-weight: normal;
      color: #0066cc;
      font-size: 11px;
    }
    
    /* 选项卡样式 */
    .match-tab {
      transition: background-color 0.2s;
      font-size: 11px;
      padding: 5px 6px !important;
    }
    
    /* 监控表格响应式字体大小 */
    #alertTable {
      font-size: 13px;
    }
    
    .match-tab.active {
      background-color: #f8f8f8 !important;
      font-weight: normal !important;
      position: relative;
      z-index: 2;
    }
    
    .match-tab:hover {
      background-color: #f5f5f5 !important;
    }
    
    .match-content {
      border: 1px solid #ddd;
      border-top: none;
      background-color: #f8f8f8;
      padding: 0;
      border-radius: 0;
    }
    
    /* 选项卡颜色区分 */
    #tab-fulltime-handicap.active {
      background-color: #e6f2ff !important; /* 蓝色调 */
      border-bottom: 2px solid #3399ff;
    }
    
    #tab-fulltime-overunder.active {
      background-color: #e6ffe6 !important; /* 绿色调 */
      border-bottom: 2px solid #33cc33;
    }
    
    #tab-halftime-handicap.active {
      background-color: #fff2e6 !important; /* 橙色调 */
      border-bottom: 2px solid #ff9933;
    }
    
    #tab-halftime-overunder.active {
      background-color: #f9e6ff !important; /* 紫色调 */
      border-bottom: 2px solid #cc66ff;
    }
    
    /* 表格内容颜色区分 */
    #content-fulltime-handicap {
      background-color: #f0f8ff; /* 浅蓝色背景 */
    }
    
    #content-fulltime-overunder {
      background-color: #f0fff0; /* 浅绿色背景 */
    }
    
    #content-halftime-handicap {
      background-color: #fff8f0; /* 浅橙色背景 */
    }
    
    #content-halftime-overunder {
      background-color: #faf0ff; /* 浅紫色背景 */
    }
    
    /* 表格头部颜色区分 */
    #fulltimeHandicapTable th {
      background-color: #cce5ff; /* 蓝色调表头 */
    }
    
    #fulltimeOverunderTable th {
      background-color: #ccffcc; /* 绿色调表头 */
    }
    
    #halftimeHandicapTable th {
      background-color: #ffe6cc; /* 橙色调表头 */
    }
    
    #halftimeOverunderTable th {
      background-color: #e6ccff; /* 紫色调表头 */
    }
    
    /* 表格行颜色交替 */
    #fulltimeHandicapTableBody tr:nth-child(4n+1), #fulltimeHandicapTableBody tr:nth-child(4n+2) {
      background-color: #e6f2ff;
    }
    
    #fulltimeOverunderTableBody tr:nth-child(4n+1), #fulltimeOverunderTableBody tr:nth-child(4n+2) {
      background-color: #e6ffe6;
    }
    
    #halftimeHandicapTableBody tr:nth-child(4n+1), #halftimeHandicapTableBody tr:nth-child(4n+2) {
      background-color: #fff2e6;
    }
    
    #halftimeOverunderTableBody tr:nth-child(4n+1), #halftimeOverunderTableBody tr:nth-child(4n+2) {
      background-color: #f9e6ff;
    }
    
    /* 移动端优化样式 */
    input[type="number"] {
      width: 40px !important;
      padding: 3px !important;
      font-size: 11px !important;
      border-radius: 0;
      border: 1px solid #ccc;
    }
    
    button {
      padding: 3px 6px !important;
      font-size: 11px !important;
      border-radius: 0;
      border: 1px solid #ccc;
      background-color: #f8f8f8;
    }
    
    /* 水位设置区域移动端优化 */
    .water-settings {
      display: flex;
      flex-direction: column;
      width: 100%;
    }
    
    /* 联赛过滤区域移动端优化 */
    #leagueFilters {
      max-height: 100px !important;
    }
    
    /* 移动端水位提示布局 */
    .mobile-settings-container {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 12px;
    }
    
    .mobile-settings-box {
      border: 1px solid #ddd;
      border-radius: 0;
      overflow: hidden;
      box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .mobile-settings-header {
      padding: 6px 8px;
      background-color: #f0f0f0;
      font-size: 12px;
      font-weight: normal;
      border-bottom: 1px solid #ddd;
      color: #333;
    }
    
    .mobile-settings-content {
      padding: 8px;
      background-color: #fafafa;
    }
    
    /* 响应式表格 */
    @media (max-width: 768px) {
      body {
        padding: 0;
        font-size: 12px;
      }
      
      .table-scroll-container {
        overflow-x: auto;
        width: 100%;
        -webkit-overflow-scrolling: touch;
      }
      
      /* 水位提示响应式布局 */
      .mobile-water-alert {
        max-height: 180px;
        overflow-y: auto;
      }
      
      /* 选项卡响应式布局 */
      .tabs-container {
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 2px;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE/Edge */
      }
      
      .tabs-container::-webkit-scrollbar {
        display: none; /* Chrome/Safari/Opera */
      }
      
      .match-tab {
        display: inline-block;
        float: none;
        font-size: 11px;
        padding: 4px 5px !important;
      }
      
      th, td {
        padding: 3px 2px;
        font-size: 12px;
        max-width: 80px;
      }
      
      .table-container {
        max-height: 250px;
      }
    
      /* 监控表格响应式字体 */
      #alertTable {
        font-size: 12px;
    }
    }
    
    /* 增强移动端体验的额外样式 */
    @media (max-width: 480px) {
      body {
        padding: 0;
        font-size: 12px;
      }
      
      .main-tab {
        padding: 6px 10px;
        font-size: 12px;
      }
      
      .mobile-settings-header {
        padding: 5px 7px;
        font-size: 12px;
      }
      
      .mobile-settings-content {
        padding: 6px;
      }
      
      input[type="number"] {
        width: 35px !important;
      }
      
      .status-item {
        font-size: 11px;
        margin-right: 6px;
      }
      
      #leagueFilters {
        max-height: 80px !important;
      }
      
      .odds-title {
        font-size: 12px;
      }
      
      /* 监控表格响应式字体 */
      #alertTable {
        font-size: 11px;
      }
    }
    
    /* 超小屏幕设备的额外调整 */
    @media (max-width: 360px) {
      /* 监控表格响应式字体 */
      #alertTable {
        font-size: 10px;
      }
    }
    
    /* 主标签页样式 */
    .main-tabs {
      display: flex;
      background-color: #fff;
      border-bottom: 1px solid #ddd;
      margin-bottom: 0px;
      overflow-x: auto;
      white-space: nowrap;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE/Edge */
    }
    
    .main-tabs::-webkit-scrollbar {
      display: none; /* Chrome/Safari/Opera */
    }
    
    .main-tab {
      padding: 4px 8px;
      font-size: 10px;
      cursor: pointer;
      transition: all 0.2s;
      text-align: center;
      flex: 1;
      border-bottom: 2px solid transparent;
    }
    
    .main-tab.active {
      border-bottom: 2px solid #1e88e5;
      font-weight: normal;
      color: #1e88e5;
    }
    
    .main-content {
      display: none;
    }
    
    .main-content.active {
      display: block;
    }

    /* 表格更新高亮效果 */
    .odds-display {
      font-size: 12px;
      line-height: 1.3;
    }

    .hg-odds {
      color: #007bff;
      font-weight: 500;
    }

    .ob-odds {
      color: #28a745;
      font-weight: 500;
    }

    .total-odds {
      color: #dc3545;
      font-weight: bold;
      margin-top: 2px;
    }

    .updated {
      background-color: #fff3cd !important;
      border: 2px solid #ffc107 !important;
      animation: highlight 2s ease-out;
    }

    @keyframes highlight {
      0% {
        background-color: #fff3cd;
        transform: scale(1.02);
      }
      100% {
        background-color: transparent;
        transform: scale(1);
      }
    }
  </style>
</head>
<body>
  <!-- 比赛标签内容 -->
  <div id="main-content-matches" class="main-content active">
    <!-- 匹配比赛结果表格 -->
    <div style="font-size: 12px; font-weight: normal; margin: 10px 0;">滚球队伍赛事匹配表格</div>
    
    <!-- 选项卡导航 - 移动端优化 -->
    <div class="tabs-container" style="display: flex; border-bottom: 1px solid #ddd; margin-bottom: -1px; position: relative; z-index: 1; overflow-x: auto; white-space: nowrap;">
      <div id="tab-fulltime-handicap" class="match-tab active" onclick="switchMatchTab('fulltime-handicap')" style="padding: 6px 10px; cursor: pointer; background-color: #f8f8f8; border: 1px solid #ddd; border-bottom: none; border-radius: 0; margin-right: 2px; font-size: 12px; font-weight: normal;">
        全场让球
  </div>
      <div id="tab-fulltime-overunder" class="match-tab" onclick="switchMatchTab('fulltime-overunder')" style="padding: 6px 10px; cursor: pointer; background-color: #f0f0f0; border: 1px solid #ddd; border-bottom: none; border-radius: 0; margin-right: 2px; font-size: 12px; font-weight: normal;">
        全场大小
  </div>
      <div id="tab-halftime-handicap" class="match-tab" onclick="switchMatchTab('halftime-handicap')" style="padding: 6px 10px; cursor: pointer; background-color: #f0f0f0; border: 1px solid #ddd; border-bottom: none; border-radius: 0; margin-right: 2px; font-size: 12px; font-weight: normal;">
        半场让球
      </div>
      <div id="tab-halftime-overunder" class="match-tab" onclick="switchMatchTab('halftime-overunder')" style="padding: 6px 10px; cursor: pointer; background-color: #f0f0f0; border: 1px solid #ddd; border-bottom: none; border-radius: 0; font-size: 12px; font-weight: normal;">
        半场大小
    </div>
  </div>
  
    <!-- 全场让球表格 - 移动端优化 -->
    <div id="content-fulltime-handicap" class="match-content" style="display: block;">
      <div class="table-scroll-container">
  <div class="table-container">
          <table id="fulltimeHandicapTable">
      <thead>
        <tr>
          <th>序号</th>
                <th>平台</th>
          <th>比赛时间</th>
                <th>比分</th>
          <th>联赛</th>
          <th>主队</th>
          <th>客队</th>
          <th>全场让球</th>
          <th class="water-section">全场让球水位</th>
          <th>相似度</th>
        </tr>
      </thead>
            <tbody id="fulltimeHandicapTableBody">
              <!-- 这里将动态填充全场让球匹配的比赛 -->
      </tbody>
    </table>
  </div>
    </div>
      </div>
      
    <!-- 全场大小表格 - 移动端优化 -->
    <div id="content-fulltime-overunder" class="match-content" style="display: none;">
      <div class="table-scroll-container">
        <div class="table-container">
          <table id="fulltimeOverunderTable">
            <thead>
              <tr>
                <th>序号</th>
                <th>平台</th>
                <th>比赛时间</th>
                <th>比分</th>
                <th>联赛</th>
                <th>主队</th>
                <th>客队</th>
          <th>全场大小</th>
          <th class="water-section">全场大小水位</th>
                <th>相似度</th>
              </tr>
            </thead>
            <tbody id="fulltimeOverunderTableBody">
              <!-- 这里将动态填充全场大小匹配的比赛 -->
            </tbody>
          </table>
      </div>
      </div>
    </div>
    
    <!-- 半场让球表格 - 移动端优化 -->
    <div id="content-halftime-handicap" class="match-content" style="display: none;">
      <div class="table-scroll-container">
        <div class="table-container">
          <table id="halftimeHandicapTable">
            <thead>
              <tr>
                <th>序号</th>
                <th>平台</th>
                <th>比赛时间</th>
                <th>比分</th>
                <th>联赛</th>
                <th>主队</th>
                <th>客队</th>
          <th>半场让球</th>
          <th class="water-section">半场让球水位</th>
          <th>相似度</th>
        </tr>
      </thead>
            <tbody id="halftimeHandicapTableBody">
              <!-- 这里将动态填充半场让球匹配的比赛 -->
      </tbody>
    </table>
  </div>
    </div>
      </div>
      
    <!-- 半场大小表格 - 移动端优化 -->
    <div id="content-halftime-overunder" class="match-content" style="display: none;">
      <div class="table-scroll-container">
        <div class="table-container">
          <table id="halftimeOverunderTable">
            <thead>
          <tr>
            <th>序号</th>
                <th>平台</th>
            <th>比赛时间</th>
                <th>比分</th>
            <th>联赛</th>
                <th>主队</th>
                <th>客队</th>
                <th>半场大小</th>
                <th class="water-section">半场大小水位</th>
                <th>相似度</th>
          </tr>
        </thead>
            <tbody id="halftimeOverunderTableBody">
              <!-- 这里将动态填充半场大小匹配的比赛 -->
        </tbody>
      </table>
      </div>
      </div>
      </div>
    </div>
    
  <!-- 监控标签内容 -->
  <div id="main-content-monitor" class="main-content" style="padding: 0; margin: 0;">
    <!-- 水位提示日志区域 - 移动端优化 -->
    <div style="margin: 0; padding: 0;">
      
      <!-- 水位提示日志表格 -->
      <div class="mobile-settings-box" style="width: 100%; box-sizing: border-box; border: none; box-shadow: none; margin-top: 0px; padding: 0;">
        <!-- 移除了监控提醒标题 -->
        <div class="mobile-water-alert" style="height: 50vh; max-height: 50vh; overflow-y: auto; min-height: 300px; width: 100%; box-sizing: border-box; padding: 0; margin: 0;">
          <table id="alertTable" style="width: 100%; border-collapse: collapse; font-size: 13px; table-layout: fixed; box-sizing: border-box; border: none; border-spacing: 0; margin: 0; padding: 0;">
        <tbody id="alertTableBody">
          <!-- 这里将动态填充提示信息 -->
        </tbody>
      </table>
    </div>
  </div>
  
      <!-- 监控提醒JSON数据 -->
      <div class="mobile-settings-box" style="margin-top: 5px;">
        <div class="mobile-settings-header" style="display: flex; justify-content: space-between; align-items: center;">
          <span>监控提醒JSON数据</span>
          <button id="submitJsonBtn" style="padding: 4px 12px; font-size: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;" title="开始/停止自动提交投注数据">📤 开始自动提交</button>
        </div>
        <div style="padding: 8px; display: flex; gap: 8px;">
          <div style="flex: 1;">
            <div style="margin-bottom: 4px; font-size: 12px; color: #666;">JSON数据</div>
            <textarea id="alertJsonData" style="width: 100%; height: 120px; padding: 8px; font-family: monospace; font-size: 12px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;" readonly></textarea>
          </div>
          <div style="flex: 1;">
            <div style="margin-bottom: 4px; font-size: 12px; color: #666;">投注字符串 (betStr)</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4px; height: 120px;">
              <div style="display: flex; flex-direction: column;">
                <div style="font-size: 10px; color: #888; margin-bottom: 2px;">全场让球</div>
                <textarea id="fthBetStrData" style="flex: 1; padding: 4px; font-family: monospace; font-size: 10px; border: 1px solid #ddd; border-radius: 2px; resize: none;" readonly placeholder="全场让球betStr..."></textarea>
              </div>
              <div style="display: flex; flex-direction: column;">
                <div style="font-size: 10px; color: #888; margin-bottom: 2px;">全场大小</div>
                <textarea id="ftouBetStrData" style="flex: 1; padding: 4px; font-family: monospace; font-size: 10px; border: 1px solid #ddd; border-radius: 2px; resize: none;" readonly placeholder="全场大小betStr..."></textarea>
              </div>
              <div style="display: flex; flex-direction: column;">
                <div style="font-size: 10px; color: #888; margin-bottom: 2px;">半场让球</div>
                <textarea id="hthBetStrData" style="flex: 1; padding: 4px; font-family: monospace; font-size: 10px; border: 1px solid #ddd; border-radius: 2px; resize: none;" readonly placeholder="半场让球betStr..."></textarea>
              </div>
              <div style="display: flex; flex-direction: column;">
                <div style="font-size: 10px; color: #888; margin-bottom: 2px;">半场大小</div>
                <textarea id="htouBetStrData" style="flex: 1; padding: 4px; font-family: monospace; font-size: 10px; border: 1px solid #ddd; border-radius: 2px; resize: none;" readonly placeholder="半场大小betStr..."></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 监控日志表格 -->
      <div class="mobile-settings-box" style="margin-top: 5px;">
        <div class="mobile-settings-header">监控日志</div>
        <div class="mobile-water-alert">
          <table id="logTable" style="width: 100%; border-collapse: collapse; font-size: 13px; table-layout: fixed;">
            <thead style="position: sticky; top: 0; z-index: 10; background-color: #f0f0f0;">
              <tr>
                <th style="width: 10%; padding: 6px; text-align: center; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">时间</th>
                <th style="width: 8%; padding: 6px; text-align: left; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">联赛</th>
                <th style="width: 10%; padding: 6px; text-align: left; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">主队</th>
                <th style="width: 10%; padding: 6px; text-align: left; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">客队</th>
                <th style="width: 12%; padding: 6px; text-align: center; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">盘口类型</th>
                <th style="width: 15%; padding: 6px; text-align: center; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">数据</th>
                <th style="width: 10%; padding: 6px; text-align: center; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">水位</th>
                <th style="width: 10%; padding: 6px; text-align: center; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">持续时间</th>
                <th style="width: 15%; padding: 6px; text-align: center; border-bottom: 1px solid #ddd; font-weight: normal; color: #333; font-size: 12px;">消失原因</th>
          </tr>
        </thead>
            <tbody id="logTableBody">
              <!-- 这里将动态填充日志信息 -->
        </tbody>
      </table>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 设置标签内容 -->
  <div id="main-content-settings" class="main-content">
    <!-- 状态信息区域 - 移动端优化 -->
    <div class="status-container">
      <div class="status-item">
      <span id="ws-dot" class="ws-dot ws-offline"></span>
      <span id="ws-text">WS未连接</span>
    </div>
    
      <div class="status-item">
        <span>处理:</span>
        <span id="process-mode" style="margin-left: 3px; font-weight: bold; color: #17a2b8;">初始化中...</span>
    </div>
    
      <div class="status-item">
        <span>HG:</span>
        <span id="hg-status" style="margin-left: 3px; margin-right: 5px; font-weight: bold; color: #dc3545;">未接收</span>
    </div>
    
      <div class="status-item">
        <span>OB:</span>
        <span id="ob-status" style="margin-left: 3px; font-weight: bold; color: #dc3545;">未接收</span>
      </div>
    </div>
    
    <!-- 相似度阈值调整 -->
    <div style="margin: 15px 0; display: flex; align-items: center; flex-wrap: wrap; gap: 5px;">
      <label for="similarityThreshold" style="font-size: 12px;">相似度阈值(%):</label>
      <input type="number" id="similarityThreshold" min="0" max="100" value="45" style="width: 45px;">
      <button onclick="updateSimilarityThreshold()">应用</button>
      <span style="margin-left: 5px; font-size: 12px; color: #666;">(下次匹配生效)</span>
    </div>
    
    <!-- 联赛过滤功能 -->
    <div style="margin: 15px 0; border: 1px solid #e0e0e0; padding: 8px; border-radius: 0; background-color: #f9f9f9;">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
        <span style="font-weight: normal; font-size: 13px;">联赛过滤</span>
        <div>
          <button onclick="selectAllLeagues(true)" style="margin-right: 5px; font-size: 11px; padding: 2px 5px;">全选</button>
          <button onclick="selectAllLeagues(false)" style="font-size: 11px; padding: 2px 5px;">全不选</button>
        </div>
      </div>
      <div id="leagueFilters" style="display: flex; flex-wrap: wrap; gap: 8px; max-height: 120px; overflow-y: auto;">
        <!-- 这里将动态填充联赛过滤选项 -->
      </div>
    </div>
    
    <!-- 赔率过滤设置 -->
    <div class="mobile-settings-box" style="margin: 15px 0;">
      <div class="mobile-settings-header">赔率过滤设置</div>
      <div class="mobile-settings-content">
        <div style="font-size: 13px; font-weight: normal; margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #444;">赔率范围设置</span>
          <button onclick="updateOddsFilter()" style="font-size: 11px; padding: 3px 8px;">应用</button>
        </div>
        
        <div style="display: flex; flex-wrap: wrap; gap: 5px; font-size: 12px;">
          <div style="display: flex; align-items: center; margin-bottom: 5px; width: 100%;">
            <span style="font-weight: normal; width: 65px; color: #555;">HG赔率:</span>
            <label for="hgMinOdds" style="color: #666; margin-right: 3px;">最小</label>
            <input type="number" id="hgMinOdds" min="0" max="3" step="0.01" value="0.8">
            
            <label for="hgMaxOdds" style="margin-left: 10px; color: #666; margin-right: 3px;">最大</label>
            <input type="number" id="hgMaxOdds" min="0" max="3" step="0.01" value="1.5">
          </div>
          
          <div style="display: flex; align-items: center; width: 100%;">
            <span style="font-weight: normal; width: 65px; color: #555;">OB赔率:</span>
            <label for="obMinOdds" style="color: #666; margin-right: 3px;">最小</label>
            <input type="number" id="obMinOdds" min="0" max="3" step="0.01" value="0.8">
            
            <label for="obMaxOdds" style="margin-left: 10px; color: #666; margin-right: 3px;">最大</label>
            <input type="number" id="obMaxOdds" min="0" max="3" step="0.01" value="1.2">
          </div>
        </div>
      </div>
    </div>
    
    <!-- 水位显示设置 -->
    <div class="mobile-settings-box" style="margin: 15px 0;">
      <div class="mobile-settings-header">水位显示设置</div>
      <div class="mobile-settings-content">
        <div style="font-size: 13px; font-weight: normal; margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #444;">水位阈值设置</span>
          <button onclick="updateWaterLevelThresholds()" style="font-size: 11px; padding: 3px 8px;">应用</button>
        </div>
        
        <div style="display: flex; flex-wrap: wrap; font-size: 12px; color: #666;">
          <div style="display: flex; align-items: center; margin-bottom: 5px; width: 100%;">
            <span style="width: 70px; font-weight: normal;">显示阈值:</span>
            <span style="font-weight: normal;">总水位 >=</span>
            <input type="number" id="waterLevelThreshold" step="0.01" value="0.92" style="margin: 0 3px;">
          </div>
          
          <div style="display: flex; align-items: center; width: 100%;">
            <span style="width: 70px; font-weight: normal;">高亮阈值:</span>
            <span style="font-weight: normal;">总水位 >=</span>
            <input type="number" id="highlightThreshold" step="0.01" value="0.97" style="margin: 0 3px;">
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 音频元素 -->
  <audio id="alertSound" preload="auto">
    <source src="y1980.wav" type="audio/wav">
    您的浏览器不支持音频播放。
  </audio>

  <!-- WebSocket连接相关的JavaScript代码 -->
  <script>
    // WebSocket连接相关变量和函数
    let socket = null;
    let wsConnected = false;
    let wsReconnectTimer = null;
    // 自动检测协议，避免混合内容问题
    const WS_PROTOCOL = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const WS_SERVER_URL = `${WS_PROTOCOL}//**************:6080`;
    const WS_RECONNECT_INTERVAL = 3000; // 重连间隔：3秒
    
    // 数据缓存
    let hgData = null;
    let obData = null;
    
    // 数据接收时间
    let lastHgReceiveTime = null;
    let lastObReceiveTime = null;
    
    // 节流控制变量
    let lastUpdateTime = 0;
    const UPDATE_INTERVAL = 250; // 250ms，即每秒4次更新
    
    // 实时检测状态
    let realtimeSyncActive = false;
    let animationFrameId = null;
    
    // 相似度阈值 (默认45%)
    let similarityThreshold = 0.45;
    
    // 水位阈值设置 (默认0.92)
    let waterLevelThreshold = 0.92;
    
    // 高亮水位阈值 (默认0.97)
    let highlightThreshold = 0.97;
    
    // 赔率过滤设置
    let minOddsFilter = 0.8;
    let maxOddsFilter = 1.5;
    
    // 拆分为HG和OB独立的赔率过滤设置
    let hgMinOddsFilter = 0.8;
    let hgMaxOddsFilter = 1.5;
    let obMinOddsFilter = 0.8;
    let obMaxOddsFilter = 1.2;
    
    // 当前活跃的提示(用于计算持续时间)
    let activeAlerts = [];
    
    // 计时器存储对象，用于跟踪不同类型计时器的起始时间
    let timerStorage = {
      // 格式: '{matchId}_{type}': timestamp
      // 例如: '英超_曼联_曼城_fth': 1621500000000
    };

    // 缓存计时器元素，避免重复DOM查询
    let cachedTimerElements = [];
    let timerCacheValid = false;
    
    // Web Worker 实例
    let matchWorker = null;
    
    // 存储所有可用的联赛名称及其选中状态
    let availableLeagues = {
      // 格式: 'league': true/false
      // 例如: '英超': true
    };
    
    // 存储过滤前的所有比赛结果，用于不重新匹配就能应用过滤
    let fullMatchResults = [];

    // 音频初始化标志
    let audioInitialized = false;

    // 初始化音频播放权限
    function initAudio() {
      if (!audioInitialized) {
        const audio = document.getElementById('alertSound');
        if (audio) {
          // 尝试播放静音音频来获取播放权限
          audio.muted = true;
          audio.play().then(() => {
            audio.muted = false;
            audioInitialized = true;
            console.log('音频播放权限已获取');
          }).catch(e => {
            console.log('获取音频播放权限失败:', e);
          });
        }
      }
    }

    // 播放提示音函数
    function playAlertSound() {
      try {
        const audio = document.getElementById('alertSound');
        if (audio) {
          console.log('尝试播放提示音...');
          audio.currentTime = 0; // 重置播放位置

          // 检查音频是否已加载
          if (audio.readyState >= 2) {
            audio.play().then(() => {
              console.log('提示音播放成功');
            }).catch(e => {
              console.log('音频播放失败:', e);
              // 尝试用户交互后播放
              document.addEventListener('click', function playOnClick() {
                audio.play().then(() => {
                  console.log('用户交互后播放成功');
                  document.removeEventListener('click', playOnClick);
                }).catch(err => {
                  console.log('用户交互后仍播放失败:', err);
                });
              }, { once: true });
            });
          } else {
            // 音频未加载完成，等待加载
            audio.addEventListener('canplay', function() {
              audio.play().then(() => {
                console.log('加载后播放成功');
              }).catch(e => {
                console.log('加载后播放失败:', e);
              });
            }, { once: true });
          }
        } else {
          console.error('找不到音频元素');
        }
      } catch (e) {
        console.error('播放提示音出错:', e);
      }
    }


    
    // 初始化Web Worker
    function initMatchWorker() {
      // 检查浏览器是否支持Web Worker
      if (typeof(Worker) !== "undefined") {
        try {
          // 创建包含Worker代码的Blob
          const workerCode = `
            // 计算两个字符串的相似度 (使用最长公共子序列算法)
            function calculateStringSimilarity(a, b) {
              if (!a || !b) return 0;
              if (a === b) return 1;
              
              // 将文本转换为字符数组
              const charListA = getCharList(a);
              const charListB = getCharList(b);
              
              const n = charListA.length;
              const m = charListB.length;
              
              if (n + m <= 0) return 0;
              
              // 计算最长公共子序列长度
              const lcsLength = getMaxLenSubStr(charListA, charListB);
              
              // 相似度 = 最长公共子序列长度 / 较长文本的长度
              return lcsLength / Math.max(n, m);
            }
            
            // 将文本分解为字符数组，处理中文等双字节字符
            function getCharList(text) {
              if (!text || text.length === 0) return [];
              
              const result = [];
              for (let i = 0; i < text.length; i++) {
                result.push(text.charCodeAt(i));
              }
              
              return result;
            }
            
            // 计算最长公共子序列长度
            function getMaxLenSubStr(x, y) {
              const n = x.length;
              const m = y.length;
              
              // 创建DP表
              const dp = Array(n + 1).fill().map(() => Array(m + 1).fill(0));
              
              // 填充DP表
              for (let i = 0; i < n; i++) {
                for (let j = 0; j < m; j++) {
                  if (x[i] === y[j]) {
                    dp[i + 1][j + 1] = dp[i][j] + 1;
                  } else {
                    dp[i + 1][j + 1] = Math.max(dp[i][j + 1], dp[i + 1][j]);
                  }
                }
              }
              
              return dp[n][m];
            }
            
            // 匹配比赛
            function matchGames(hgData, obData, similarityThreshold) {
              if (!hgData || !hgData.matches || !obData || !obData.matches) {
                return [];
              }
              
              const matchResults = [];
              
              // 对每个HG比赛尝试寻找OB匹配
              for (const hgMatch of hgData.matches) {
                let bestMatch = null;
                let bestTotalScore = 0;
                
                // 获取HG比赛信息
                const hgHome = hgMatch.teams && hgMatch.teams.home;
                const hgAway = hgMatch.teams && hgMatch.teams.away;
                const hgLeague = hgMatch.league || '';
                // 确保获取比分数据
                const hgScore = hgMatch.score || '0-0';
                
                if (!hgHome || !hgAway) continue;
                
                // 遍历OB比赛寻找最佳匹配
                for (const obMatch of obData.matches) {
                  const obHome = obMatch.teams && obMatch.teams.home;
                  const obAway = obMatch.teams && obMatch.teams.away;
                  const obLeague = obMatch.league || '';
                  // 确保获取比分数据
                  const obScore = obMatch.score || '0-0';
                  
                  if (!obHome || !obAway) continue;
                  
                  // 计算各项相似度
                  const homeSimilarity = calculateStringSimilarity(hgHome, obHome);
                  const awaySimilarity = calculateStringSimilarity(hgAway, obAway);
                  const leagueSimilarity = calculateStringSimilarity(hgLeague, obLeague);
                  
                  // 计算加权总分 (确保结果最高为100%)
                  // 主队相似度35%，客队相似度35%，联赛相似度30%
                  const totalScore = (homeSimilarity * 0.35) + (awaySimilarity * 0.35) + (leagueSimilarity * 0.3);
                  
                  // 如果总分更高，更新最佳匹配
                  if (totalScore > bestTotalScore) {
                    bestTotalScore = totalScore;
                    bestMatch = {
                      match: obMatch,
                      homeSimilarity: homeSimilarity,
                      awaySimilarity: awaySimilarity,
                      leagueSimilarity: leagueSimilarity,
                      totalScore: totalScore
                    };
                  }
                }
                
                // 如果找到匹配，添加到结果
                if (bestMatch && bestMatch.totalScore >= similarityThreshold) {
                  matchResults.push({
                    hg: hgMatch,
                    ob: bestMatch.match,
                    homeSimilarity: bestMatch.homeSimilarity,
                    awaySimilarity: bestMatch.awaySimilarity,
                    leagueSimilarity: bestMatch.leagueSimilarity,
                    totalScore: bestMatch.totalScore
                  });
                }
              }
              
              return matchResults;
            }
            
            // Worker消息处理
            self.onmessage = function(e) {
              const { type, hgData, obData, similarityThreshold } = e.data;
              
              if (type === 'matchGames') {
                // 执行匹配
                const results = matchGames(hgData, obData, similarityThreshold);
                
                // 将结果发送回主线程
                self.postMessage({
                  type: 'matchResults',
                  results: results
                });
              }
            };
          `;
          
          // 创建包含Worker代码的Blob URL
          const blob = new Blob([workerCode], { type: 'application/javascript' });
          const workerUrl = URL.createObjectURL(blob);
          
          // 创建Worker
          matchWorker = new Worker(workerUrl);
          
          // 处理Worker发送的消息
          matchWorker.onmessage = function(e) {
            if (e.data.type === 'matchResults') {
              // 更新表格
              updateMatchTable(e.data.results);
            }
          };
          
          // 处理Worker错误
          matchWorker.onerror = function(error) {
            console.error('Worker error:', error);
            // 如果Worker出错，回退到单线程模式
            matchWorker = null;
            updateProcessMode(false);
          };
          
          // 更新处理模式显示
          updateProcessMode(true);
        } catch (error) {
          console.error('Failed to initialize Web Worker:', error);
          matchWorker = null;
          updateProcessMode(false);
        }
      } else {
        console.warn('Browser does not support Web Workers, using single-threaded mode');
        updateProcessMode(false);
      }
    }
    
    // 初始化WebSocket连接
    function initWebSocket() {
      if (socket) {
        // 清理之前的连接
        socket.onclose = null;
        socket.onerror = null;
        socket.onmessage = null;
        socket.onopen = null;
        socket.close();
        socket = null;
      }
      
      // 更新WebSocket状态UI
      updateWsStatus(false);
      
      try {
        socket = new WebSocket(WS_SERVER_URL);
        
        // 连接成功事件
        socket.onopen = function() {
          wsConnected = true;
          updateWsStatus(true);
          
          // 清除重连定时器
          if (wsReconnectTimer) {
            clearTimeout(wsReconnectTimer);
            wsReconnectTimer = null;
          }
          
          
          
        };
        
        // 连接关闭事件
        socket.onclose = function() {
          wsConnected = false;
          updateWsStatus(false);
          
          
          
          
          // 自动重连
          if (!wsReconnectTimer) {
            wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
          }
        };
        
        // 连接错误事件
        socket.onerror = function(error) {
          wsConnected = false;
          updateWsStatus(false);
          
          
          
          
          // 自动重连
          if (!wsReconnectTimer) {
            wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
          }
        };
        
        // 接收消息事件 - 优化版本
        socket.onmessage = function(event) {
          try {
            // 直接处理数据，减少不必要的检查
            if (event.data instanceof Blob) {
              // Blob快速处理
              event.data.text().then(text => {
                try {
                  const decryptedData = decryptData(text);
                  if (decryptedData) {
                    processData(decryptedData);
                  }
                } catch (e) {
                  console.log('处理Blob数据错误:', e);
                }
              }).catch((e) => {
                console.log('读取Blob数据错误:', e);
              });
            } else {
              // 文本快速处理
              const decryptedData = decryptData(event.data);
              if (decryptedData) {
                processData(decryptedData);
              }
            }
          } catch (e) {
            console.log('处理WebSocket消息错误:', e);
          }
        };
      } catch (error) {
        wsConnected = false;
        updateWsStatus(false);
        
        // 自动重连
        if (!wsReconnectTimer) {
          wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
        }
      }
    }
    
    // 更新WebSocket状态UI
    function updateWsStatus(isConnected) {
      const wsStatusDot = document.getElementById('ws-dot');
      const wsStatusText = document.getElementById('ws-text');
      
      if (wsStatusDot) {
        wsStatusDot.className = isConnected ? 'ws-dot ws-online' : 'ws-dot ws-offline';
      }
      
      if (wsStatusText) {
        wsStatusText.textContent = isConnected ? 'WS已连接' : 'WS未连接';
      }
    }
    
    // 更新处理模式显示
    function updateProcessMode(isParallel) {
      const processModeText = document.getElementById('process-mode');
      
      if (processModeText) {
        if (isParallel) {
          processModeText.textContent = '并行处理 (Web Worker)';
          processModeText.style.color = '#28a745'; // 绿色
        } else {
          processModeText.textContent = '单线程处理';
          processModeText.style.color = '#ffc107'; // 黄色
        }
      }
    }
    
    // 数据处理函数 - 不再使用加密
    function encryptData(data) {
      try {
        // 将数据转换为字符串
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);
        
        // 使用固定密码进行简单加密
        const password = "mddh.2d33gg";
        let encrypted = "";
        
        // 简单的XOR加密
        for (let i = 0; i < dataString.length; i++) {
          const charCode = dataString.charCodeAt(i) ^ password.charCodeAt(i % password.length);
          encrypted += String.fromCharCode(charCode);
        }
        
        // 转换为Base64以便传输 - 使用encodeURIComponent处理Unicode字符
        return btoa(encodeURIComponent(encrypted).replace(/%([0-9A-F]{2})/g, (match, p1) => {
          return String.fromCharCode('0x' + p1);
        }));
      } catch (error) {
        console.error('加密失败:', error);
        return null;
      }
    }
    
    // 数据解析函数 - 使用固定密码解密
    function decryptData(data) {
      if (!data) return null;
      
      try {
        // 检查数据是否为有效的Base64格式
        const base64Regex = /^[A-Za-z0-9+/=]+$/;
        const isBase64 = typeof data === 'string' && base64Regex.test(data.replace(/\s/g, ''));
        
        // 如果数据看起来是Base64格式，尝试解密
        if (isBase64) {
          try {
            // 使用固定密码解密
            const password = "mddh.2d33gg";
            
            // 从Base64转回原始加密字符串
            let encrypted;
            try {
              // 先进行Base64解码
              encrypted = atob(data);
            } catch (base64Error) {
              console.error('Base64解码失败:', base64Error);
              // 如果Base64解码失败，尝试直接解析为JSON
              return typeof data === 'string' ? JSON.parse(data) : data;
            }
            
            // OB.html中的加密逻辑是:
            // 1. 使用XOR加密原始字符串
            // 2. 使用encodeURIComponent处理加密后的字符串
            // 3. 使用正则表达式将%XX转换为实际字符
            // 4. 使用btoa进行Base64编码
            
            // 这里我们需要逆向这个过程:
            // 1. 已经使用atob进行Base64解码
            // 2. 现在需要将字符转回%XX格式
            // 3. 使用decodeURIComponent解码
            // 4. 使用XOR解密
            
            let decodedStr = "";
            try {
              // 将每个字符转换为URL编码格式(%XX)
              for (let i = 0; i < encrypted.length; i++) {
                const hex = encrypted.charCodeAt(i).toString(16).toUpperCase().padStart(2, '0');
                decodedStr += "%" + hex;
              }
              
              // 使用decodeURIComponent解码
              encrypted = decodeURIComponent(decodedStr);
            } catch (decodeError) {
              console.error('URL解码失败，使用原始字符串继续:', decodeError);
              // 解码失败时，尝试直接使用原始Base64解码后的字符串
            }
            
            let decrypted = "";
            
            // 使用XOR操作解密
            for (let i = 0; i < encrypted.length; i++) {
              const charCode = encrypted.charCodeAt(i) ^ password.charCodeAt(i % password.length);
              decrypted += String.fromCharCode(charCode);
            }
            
            // 检查解密后的字符串是否为有效的JSON格式
            if (decrypted.trim().startsWith('{') || decrypted.trim().startsWith('[')) {
              // 尝试解析为JSON对象
              return JSON.parse(decrypted);
            } else {
              console.warn('解密后的数据不是有效的JSON格式:', decrypted.substring(0, 100));
              // 如果解密后不是有效的JSON格式，尝试直接解析原始数据
              return typeof data === 'string' ? JSON.parse(data) : data;
            }
          } catch (decryptError) {
            console.error('解密失败:', decryptError);
            // 如果解密失败，尝试直接解析为JSON
            return typeof data === 'string' ? JSON.parse(data) : data;
          }
        } else {
          // 如果不是Base64格式，直接尝试解析为JSON
          return typeof data === 'string' ? JSON.parse(data) : data;
        }
      } catch (error) {
        console.error('数据解析错误:', error);
        // 返回null而不是抛出错误，确保程序继续运行
        return null;
      }
    }
    
    // 更新平台数据状态
    function updateDataStatus() {
      const hgStatusElem = document.getElementById('hg-status');
      const obStatusElem = document.getElementById('ob-status');
      
      if (hgStatusElem) {
        if (hgData) {
          const timeString = lastHgReceiveTime ? lastHgReceiveTime.toLocaleTimeString() : '未知';
          hgStatusElem.textContent = `已接收 (${timeString})`;
          hgStatusElem.style.color = '#28a745'; // 绿色
        } else {
          hgStatusElem.textContent = '未接收';
          hgStatusElem.style.color = '#dc3545'; // 红色
        }
      }
      
      if (obStatusElem) {
        if (obData) {
          const timeString = lastObReceiveTime ? lastObReceiveTime.toLocaleTimeString() : '未知';
          obStatusElem.textContent = `已接收 (${timeString})`;
          obStatusElem.style.color = '#28a745'; // 绿色
        } else {
          obStatusElem.textContent = '未接收';
          obStatusElem.style.color = '#dc3545'; // 红色
        }
      }
    }
    
    // 实时检测数据并尝试匹配 (使用requestAnimationFrame实现真正的实时)
    function checkAndMatchData() {
      try {
        // 检查页面是否仍处于活动状态
        if (typeof document === 'undefined' || !document) return;
        
        // 应用节流控制，限制更新频率
        const now = performance.now();
        if (now - lastUpdateTime < UPDATE_INTERVAL) {
          // 如果间隔时间太短，跳过本次更新，但仍然安排下一次检查
          if (realtimeSyncActive) {
            animationFrameId = requestAnimationFrame(checkAndMatchData);
          }
          return;
        }
        
        // 更新最后处理时间
        lastUpdateTime = now;
        
        // 更新数据状态显示
        updateDataStatus();
        
        // 如果两种数据都有，尝试匹配
        if (hgData && obData) {
          if (matchWorker) {
            // 使用Web Worker并行处理
            matchWorker.postMessage({
              type: 'matchGames',
              hgData: hgData,
              obData: obData,
              similarityThreshold: similarityThreshold
            });
          } else {
            // 回退到单线程处理
            matchGames();
          }
        } else {
          // 仅显示有数据的平台数据
          console.log("等待数据: " + (hgData ? "已有HG数据" : "无HG数据") + ", " + (obData ? "已有OB数据" : "无OB数据"));
        }
        
        // 继续实时同步循环
        if (realtimeSyncActive) {
          try {
            animationFrameId = requestAnimationFrame(checkAndMatchData);
          } catch (e) {
            console.log('实时同步循环错误:', e);
          }
        }
      } catch (e) {
        console.log('数据检测和匹配错误:', e);
        // 出错后仍然尝试继续同步，但使用setTimeout避免立即再次出错
        if (realtimeSyncActive) {
          setTimeout(() => {
            try {
              if (realtimeSyncActive) {
                animationFrameId = requestAnimationFrame(checkAndMatchData);
              }
            } catch (e) {
              console.log('重新启动同步错误:', e);
            }
          }, 1000);
        }
      }
    }
    
    // 启动实时同步
    function startRealtimeSync() {
      if (!realtimeSyncActive) {
        realtimeSyncActive = true;
        try {
          animationFrameId = requestAnimationFrame(checkAndMatchData);
          console.log("启动实时同步");
        } catch (e) {
          console.log("启动实时同步出错:", e);
          // 出错后尝试使用setTimeout作为备选方案
          setTimeout(() => {
            try {
              if (realtimeSyncActive) {
                animationFrameId = requestAnimationFrame(checkAndMatchData);
              }
            } catch (e) {
              console.log('备选同步方式启动错误:', e);
            }
          }, 1000);
        }
      }
    }
    
    // 停止实时同步
    function stopRealtimeSync() {
      if (realtimeSyncActive) {
        realtimeSyncActive = false;
        try {
          if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
          }
          console.log("停止实时同步");
        } catch (e) {
          console.log("停止实时同步出错:", e);
          // 确保animationFrameId被重置
          animationFrameId = null;
        }
      }
    }
    
    // 处理接收到的数据
    function processData(data) {
      // 过滤掉心跳消息
      if (data && data.type === 'heartbeat') {
        return;
      }
      
      // 获取当前时间（精确到秒）
      const now = new Date();
      const timeStr = now.toTimeString().split(' ')[0]; // 格式：HH:MM:SS
      
      // 检查数据格式和类型
      if (data) {
        // 根据类型保存数据
        if (isHGData(data)) {
          console.log(timeStr, "类型 HG", data);
          hgData = data;
          lastHgReceiveTime = now;
        } else if (isOBData(data)) {
          console.log(timeStr, "类型 OB", data);
          obData = data;
          lastObReceiveTime = now;
        } else {
          console.log(timeStr, "类型 未知", data);
        }
        
        // 更新数据状态显示
        updateDataStatus();
        
        // 如果两种数据都有了，尝试匹配
        if (hgData && obData) {
          if (matchWorker) {
            // 使用Web Worker并行处理
            matchWorker.postMessage({
              type: 'matchGames',
              hgData: hgData,
              obData: obData,
              similarityThreshold: similarityThreshold
            });
          } else {
            // 回退到单线程处理
            matchGames();
          }
        }
      }
    }
    
    // 判断是否为HG数据
    function isHGData(data) {
      // 检查是否有顶级标识字段
      if (data.标识 === 'HG' || data.标识 === 'hg') {
        return true;
      }
      
      // 如果没有明确标识，则返回false
      return false;
    }
    
    // 判断是否为OB数据
    function isOBData(data) {
      // 检查是否有顶级标识字段
      if (data.标识 === 'OB' || data.标识 === 'ob') {
        return true;
      }
      
      // 如果没有明确标识，则返回false
      return false;
    }
    
    // 匹配比赛
    function matchGames() {
      if (!hgData || !hgData.matches || !obData || !obData.matches) {
        return;
      }
      
      const matchResults = [];
      
      // 对每个HG比赛尝试寻找OB匹配
      for (const hgMatch of hgData.matches) {
        let bestMatch = null;
        let bestTotalScore = 0;
        
        // 获取HG比赛信息
        const hgHome = hgMatch.teams && hgMatch.teams.home;
        const hgAway = hgMatch.teams && hgMatch.teams.away;
        const hgLeague = hgMatch.league || '';
        const hgDateTime = hgMatch.dateTime || '';
        // 确保获取比分数据
        const hgScore = hgMatch.score || '0-0';
        
        if (!hgHome || !hgAway) continue;
        
        // 遍历OB比赛寻找最佳匹配
        for (const obMatch of obData.matches) {
          const obHome = obMatch.teams && obMatch.teams.home;
          const obAway = obMatch.teams && obMatch.teams.away;
          const obLeague = obMatch.league || '';
          const obDateTime = obMatch.dateTime || '';
          // 确保获取比分数据
          const obScore = obMatch.score || '0-0';
          
          if (!obHome || !obAway) continue;
          
          // 计算各项相似度
          const homeSimilarity = calculateStringSimilarity(hgHome, obHome);
          const awaySimilarity = calculateStringSimilarity(hgAway, obAway);
          const leagueSimilarity = calculateStringSimilarity(hgLeague, obLeague);
          
          // 计算加权总分 (确保结果最高为100%)
          // 主队相似度35%，客队相似度35%，联赛相似度30%
          const totalScore = (homeSimilarity * 0.35) + (awaySimilarity * 0.35) + (leagueSimilarity * 0.3);
          
          // 如果总分更高，更新最佳匹配
          if (totalScore > bestTotalScore) {
            bestTotalScore = totalScore;
            bestMatch = {
              match: obMatch,
              homeSimilarity: homeSimilarity,
              awaySimilarity: awaySimilarity,
              leagueSimilarity: leagueSimilarity,
              totalScore: totalScore
            };
          }
        }
        
        // 如果找到匹配，添加到结果
        if (bestMatch && bestMatch.totalScore >= similarityThreshold) {
          matchResults.push({
            hg: hgMatch,
            ob: bestMatch.match,
            homeSimilarity: bestMatch.homeSimilarity,
            awaySimilarity: bestMatch.awaySimilarity,
            leagueSimilarity: bestMatch.leagueSimilarity,
            totalScore: bestMatch.totalScore
          });
        }
      }
      
      // 更新表格
      updateMatchTable(matchResults);
    }
    
    // 更新匹配表格
    function updateMatchTable(matchResults) {
      // 保存完整的比赛结果，用于过滤
      fullMatchResults = [];
      
      // 临时存储所有联赛
      const leaguesSet = new Set();
      
      // 收集当前数据中所有的联赛
      matchResults.forEach(match => {
        const league = match.hg.league || '';
        if (league) {
          leaguesSet.add(league);
        }
      });
      
      // 记录原有的联赛选择状态
      const originalLeagueSelections = { ...availableLeagues };
      
      // 清理不再存在的联赛
      const currentLeagues = Array.from(leaguesSet);
      Object.keys(availableLeagues).forEach(league => {
        if (!leaguesSet.has(league)) {
          delete availableLeagues[league];
        }
      });
      
      // 预处理matchResults，计算并存储所需的所有数据
      matchResults.forEach(match => {
        // 收集HG联赛名称
        const league = match.hg.league || '';
        if (league) {
          // 如果这个联赛之前没见过，默认为不选中状态
          if (availableLeagues[league] === undefined) {
            availableLeagues[league] = false;
          }
        }
        
        // 预计算所有需要的值，这样在过滤后可以直接使用而不需要重新计算
        // 使用新的多盘口匹配算法，获取所有匹配的盘口组合
        
        // 全场让球所有匹配
      const fullTimeHandicapMatches = match.hg.fullTimeHandicap && match.ob.fullTimeHandicap ?
        findAllMatchingHandicaps(match.hg.fullTimeHandicap, match.ob.fullTimeHandicap) : [];
        
        // 全场大小球所有匹配
      const fullTimeOverUnderMatches = match.hg.fullTimeOverUnder && match.ob.fullTimeOverUnder ?
        findAllMatchingOverUnders(match.hg.fullTimeOverUnder, match.ob.fullTimeOverUnder) : [];
        
        // 半场让球所有匹配
      const halfTimeHandicapMatches = match.hg.halfTimeHandicap && match.ob.halfTimeHandicap ?
        findAllMatchingHandicaps(match.hg.halfTimeHandicap, match.ob.halfTimeHandicap) : [];
        
        // 半场大小球所有匹配
      const halfTimeOverUnderMatches = match.hg.halfTimeOverUnder && match.ob.halfTimeOverUnder ?
        findAllMatchingOverUnders(match.hg.halfTimeOverUnder, match.ob.halfTimeOverUnder) : [];

        const processedMatch = {
          ...match,  // 保留原始比赛数据
          
          // 存储所有匹配的盘口组合数据
          // 全场让球所有匹配
          allFullTimeHandicapMatches: fullTimeHandicapMatches,
          
          // 全场大小球所有匹配
          allFullTimeOverUnderMatches: fullTimeOverUnderMatches,
          
          // 半场让球所有匹配
          allHalfTimeHandicapMatches: halfTimeHandicapMatches,
          
          // 半场大小球所有匹配
          allHalfTimeOverUnderMatches: halfTimeOverUnderMatches,
          
          // 为了兼容现有代码，保留最佳匹配的数据
          // 全场让球
          matchedHGHandicap: fullTimeHandicapMatches.length > 0 ? fullTimeHandicapMatches[0].hgLine : "无匹配盘口",
          matchedOBHandicap: fullTimeHandicapMatches.length > 0 ? fullTimeHandicapMatches[0].obLine : "无匹配盘口",
          hgHomeObAwayTotal: fullTimeHandicapMatches.length > 0 ? fullTimeHandicapMatches[0].homeAwayWater : "0.00",
          hgAwayObHomeTotal: fullTimeHandicapMatches.length > 0 ? fullTimeHandicapMatches[0].awayHomeWater : "0.00",
          
          // 全场大小球
          matchedHGOverUnder: fullTimeOverUnderMatches.length > 0 ? fullTimeOverUnderMatches[0].hgLine : "无匹配盘口",
          matchedOBOverUnder: fullTimeOverUnderMatches.length > 0 ? fullTimeOverUnderMatches[0].obLine : "无匹配盘口",
          hgOverObUnderTotal: fullTimeOverUnderMatches.length > 0 ? fullTimeOverUnderMatches[0].overUnderWater : "0.00",
          hgUnderObOverTotal: fullTimeOverUnderMatches.length > 0 ? fullTimeOverUnderMatches[0].underOverWater : "0.00",
                                                                                       
          // 半场让球
          matchedHGHalfTimeHandicap: halfTimeHandicapMatches.length > 0 ? halfTimeHandicapMatches[0].hgLine : "无匹配盘口",
          matchedOBHalfTimeHandicap: halfTimeHandicapMatches.length > 0 ? halfTimeHandicapMatches[0].obLine : "无匹配盘口",
          hgHalfHomeObHalfAwayTotal: halfTimeHandicapMatches.length > 0 ? halfTimeHandicapMatches[0].homeAwayWater : "0.00",
          hgHalfAwayObHalfHomeTotal: halfTimeHandicapMatches.length > 0 ? halfTimeHandicapMatches[0].awayHomeWater : "0.00",
          
          // 半场大小球
          matchedHGHalfTimeOverUnder: halfTimeOverUnderMatches.length > 0 ? halfTimeOverUnderMatches[0].hgLine : "无匹配盘口",
          matchedOBHalfTimeOverUnder: halfTimeOverUnderMatches.length > 0 ? halfTimeOverUnderMatches[0].obLine : "无匹配盘口",
          hgHalfTimeOverObUnderTotal: halfTimeOverUnderMatches.length > 0 ? halfTimeOverUnderMatches[0].overUnderWater : "0.00",
          hgHalfTimeUnderObOverTotal: halfTimeOverUnderMatches.length > 0 ? halfTimeOverUnderMatches[0].underOverWater : "0.00"
        };
        
        // 添加到完整比赛结果中
        fullMatchResults.push(processedMatch);
      });
      
      // 检查联赛列表是否有变化（由于新增或删除的联赛）
      let leaguesChanged = Object.keys(availableLeagues).length !== Object.keys(originalLeagueSelections).length;
      
      // 如果联赛数量没有变化，还需要检查是否有新增的联赛（替换了原有的联赛）
      if (!leaguesChanged) {
        for (const league of leaguesSet) {
          if (originalLeagueSelections[league] === undefined) {
            leaguesChanged = true;
            break;
          }
        }
      }
      
      // 只有在联赛列表变化时才更新UI
      if (leaguesChanged) {
        // 更新联赛过滤UI
        updateLeagueFiltersUI();
      }
      
      // 先应用联赛过滤器获取过滤后的比赛
      const filteredResults = fullMatchResults.filter(match => {
        const league = match.hg.league || '';
        return availableLeagues[league] === true;
      });
      
      // 处理被过滤掉的联赛相关的提示
      handleFilteredLeagueAlerts(filteredResults);
      
      // 更新UI显示过滤后的比赛
      updateMatchTableUI(filteredResults);
      
      // 只为过滤后的比赛准备提示数据
      prepareAndUpdateAlerts(filteredResults);
    }
    
    // 为过滤后的比赛准备提示数据并更新提示表格
    function prepareAndUpdateAlerts(filteredMatches) {
      // 获取当前所有活跃的提示ID
      const currentAlertIds = new Set();
      activeAlerts.forEach(alert => {
        const matchId = `${alert.league}_${alert.teams.home}_${alert.teams.away}`;
        currentAlertIds.add(matchId);
      });
      
      // 保存当前有高亮的提示，用于后续检查是否消失
      const currentHighlightAlerts = activeAlerts.filter(alert => {
        return (
          parseFloat(alert.fullTimeHandicap.totalOdds) > highlightThreshold ||
          parseFloat(alert.fullTimeOverUnder.totalOdds) > highlightThreshold ||
          parseFloat(alert.halfTimeHandicap.totalOdds) > highlightThreshold ||
          parseFloat(alert.halfTimeOverUnder.totalOdds) > highlightThreshold
        );
      });
      
      // 首先同步活跃提示与当前比赛数据，确保活跃提示的内容与当前比赛数据保持一致
      syncActiveAlertsWithCurrentMatches(filteredMatches);
      
      // 准备提示数据
      // 获取当前时间
      const now = new Date();
      const alerts = [];
      
      // 用于按赛事分组的对象
      const matchGroups = {};
      
      // 初始化达到水位阈值的计时器的存储
      for (const match of filteredMatches) {
        const matchId = `${match.hg.league || '未知'}_${match.hg.teams.home}_${match.hg.teams.away}`;
        
        // 预转换数值，避免重复parseFloat
        const hgHomeObAwayTotal = parseFloat(match.hgHomeObAwayTotal) || 0;
        const hgAwayObHomeTotal = parseFloat(match.hgAwayObHomeTotal) || 0;
        const hgOverObUnderTotal = parseFloat(match.hgOverObUnderTotal) || 0;
        const hgUnderObOverTotal = parseFloat(match.hgUnderObOverTotal) || 0;
        const hgHalfHomeObHalfAwayTotal = parseFloat(match.hgHalfHomeObHalfAwayTotal) || 0;
        const hgHalfAwayObHalfHomeTotal = parseFloat(match.hgHalfAwayObHalfHomeTotal) || 0;
        const hgHalfTimeOverObUnderTotal = parseFloat(match.hgHalfTimeOverObUnderTotal) || 0;
        const hgHalfTimeUnderObOverTotal = parseFloat(match.hgHalfTimeUnderObOverTotal) || 0;

        const currentTime = now.getTime();

        // 只为达到水位阈值的盘口创建计时器存储（优化条件检查）
        if (hgHomeObAwayTotal >= waterLevelThreshold || hgAwayObHomeTotal >= waterLevelThreshold) {
          const key = `${matchId}_fth`;
          if (!timerStorage[key]) timerStorage[key] = currentTime;
        }

        if (hgOverObUnderTotal >= waterLevelThreshold || hgUnderObOverTotal >= waterLevelThreshold) {
          const key = `${matchId}_ftou`;
          if (!timerStorage[key]) timerStorage[key] = currentTime;
        }

        if (hgHalfHomeObHalfAwayTotal >= waterLevelThreshold || hgHalfAwayObHalfHomeTotal >= waterLevelThreshold) {
          const key = `${matchId}_hth`;
          if (!timerStorage[key]) timerStorage[key] = currentTime;
        }

        if (hgHalfTimeOverObUnderTotal >= waterLevelThreshold || hgHalfTimeUnderObOverTotal >= waterLevelThreshold) {
          const key = `${matchId}_htou`;
          if (!timerStorage[key]) timerStorage[key] = currentTime;
        }
      }
      
      // 只为过滤后的比赛准备提示数据
      filteredMatches.forEach(match => {
        // 创建赛事唯一标识
        const matchKey = `${match.hg.league || '未知'}_${match.hg.teams.home}_${match.hg.teams.away}`;

        // 预转换数值，避免重复parseFloat（移到forEach内部）
        const hgHomeObAwayTotal = parseFloat(match.hgHomeObAwayTotal) || 0;
        const hgAwayObHomeTotal = parseFloat(match.hgAwayObHomeTotal) || 0;
        const hgOverObUnderTotal = parseFloat(match.hgOverObUnderTotal) || 0;
        const hgUnderObOverTotal = parseFloat(match.hgUnderObOverTotal) || 0;
        const hgHalfHomeObHalfAwayTotal = parseFloat(match.hgHalfHomeObHalfAwayTotal) || 0;
        const hgHalfAwayObHalfHomeTotal = parseFloat(match.hgHalfAwayObHalfHomeTotal) || 0;
        const hgHalfTimeOverObUnderTotal = parseFloat(match.hgHalfTimeOverObUnderTotal) || 0;
        const hgHalfTimeUnderObOverTotal = parseFloat(match.hgHalfTimeUnderObOverTotal) || 0;

        // 如果这个赛事还没有记录，创建一个新的记录
        if (!matchGroups[matchKey]) {
          matchGroups[matchKey] = {
            league: match.hg.league || '未知',
            matchTime: match.hg.dateTime || '未知',
            teams: {
              home: match.hg.teams.home,
              away: match.hg.teams.away
            },
            hgTeams: {
              home: match.hg.teams.home,
              away: match.hg.teams.away
            },
            obTeams: {
              home: match.ob.teams.home,
              away: match.ob.teams.away
            },
            // 添加HG的ecId和gid信息，用于生成投注字符串
            hgEcId: match.hg.id || '',
            hgGid: match.hg.id || '', // 使用ecId作为GID的替代
            fullTimeHandicap: {
              hgData: '',
              obData: '',
              totalOdds: ''
            },
            fullTimeOverUnder: {
              hgData: '',
              obData: '',
              totalOdds: ''
            },
            halfTimeHandicap: {
              hgData: '',
              obData: '',
              totalOdds: ''
            },
            halfTimeOverUnder: {
              hgData: '',
              obData: '',
              totalOdds: ''
            },
            alertTime: new Date().toLocaleTimeString()
          };
        }
        
        // 检查全场让球水位 - 使用最佳匹配结果（使用预转换的数值）
        const maxHandicapWater = Math.max(hgHomeObAwayTotal, hgAwayObHomeTotal);
        if (maxHandicapWater >= waterLevelThreshold) {
          // 直接从匹配结果中获取最佳匹配的盘口数据（包含投注字符串）
          const bestMatch = match.allFullTimeHandicapMatches && match.allFullTimeHandicapMatches.length > 0 ? match.allFullTimeHandicapMatches[0] : null;

          if (bestMatch && bestMatch.hgItem && bestMatch.obItem) {
            const hgItem = bestMatch.hgItem;
            const obItem = bestMatch.obItem;
            const hgHomeOdds = parseFloat(hgItem.homeOdds) || 0;
            const hgAwayOdds = parseFloat(hgItem.awayOdds) || 0;
            const obHomeOdds = parseFloat(obItem.homeOdds) || 0;
          const obAwayOdds = parseFloat(obItem.awayOdds) || 0;

            // 选择水位更高的方向（使用预转换的数值）
            if (hgHomeObAwayTotal >= hgAwayObHomeTotal) {
              // 主客方向水位更高
          if (isOddsInFilterRange(hgHomeOdds, true) && isOddsInFilterRange(obAwayOdds, false)) {
            matchGroups[matchKey].fullTimeHandicap = {
                  hgData: `主|${convertHandicapToFraction(hgItem.homeHandicap)}|${hgHomeOdds.toFixed(2)}|${hgItem.homebetStr || ''}`,
                  obData: `客|${convertHandicapToFraction(obItem.awayHandicap)}|${obAwayOdds.toFixed(2)}`,
              totalOdds: match.hgHomeObAwayTotal
            };
          }
            } else {
              // 客主方向水位更高
          if (isOddsInFilterRange(hgAwayOdds, true) && isOddsInFilterRange(obHomeOdds, false)) {
            matchGroups[matchKey].fullTimeHandicap = {
                  hgData: `客|${convertHandicapToFraction(hgItem.awayHandicap)}|${hgAwayOdds.toFixed(2)}|${hgItem.awaybetStr || ''}`,
                  obData: `主|${convertHandicapToFraction(obItem.homeHandicap)}|${obHomeOdds.toFixed(2)}`,
              totalOdds: match.hgAwayObHomeTotal
            };
          }
            }
                     }
        }
        
         // 检查全场大小水位 - 使用最佳匹配结果（使用预转换的数值）
         const maxOverUnderWater = Math.max(hgOverObUnderTotal, hgUnderObOverTotal);
         if (maxOverUnderWater >= waterLevelThreshold) {
           // 直接从匹配结果中获取最佳匹配的盘口数据（包含投注字符串）
           const bestMatch = match.allFullTimeOverUnderMatches && match.allFullTimeOverUnderMatches.length > 0 ? match.allFullTimeOverUnderMatches[0] : null;

           if (bestMatch && bestMatch.hgItem && bestMatch.obItem) {
             const hgItem = bestMatch.hgItem;
             const obItem = bestMatch.obItem;
             const hgOverOdds = parseFloat(hgItem.overOdds) || 0;
             const hgUnderOdds = parseFloat(hgItem.underOdds) || 0;
             const obOverOdds = parseFloat(obItem.overOdds) || 0;
          const obUnderOdds = parseFloat(obItem.underOdds) || 0;

             // 选择水位更高的方向（使用预转换的数值）
             if (hgOverObUnderTotal >= hgUnderObOverTotal) {
               // 大小方向水位更高
          if (isOddsInFilterRange(hgOverOdds, true) && isOddsInFilterRange(obUnderOdds, false)) {
            const hgOverTotal = hgItem.overTotal ? hgItem.overTotal.replace(/^大\s*/, '').trim() : '';
            matchGroups[matchKey].fullTimeOverUnder = {
                   hgData: `大|${convertHandicapToFraction(hgOverTotal)}|${hgOverOdds.toFixed(2)}|${hgItem.overbetStr || ''}`,
                   obData: `小|${convertHandicapToFraction(obItem.underTotal ? obItem.underTotal.replace(/^小\s*/, '').trim() : '')}|${obUnderOdds.toFixed(2)}`,
              totalOdds: match.hgOverObUnderTotal
            };
          }
             } else {
               // 小大方向水位更高
          if (isOddsInFilterRange(hgUnderOdds, true) && isOddsInFilterRange(obOverOdds, false)) {
            const hgUnderTotal = hgItem.underTotal ? hgItem.underTotal.replace(/^小\s*/, '').trim() : '';
            matchGroups[matchKey].fullTimeOverUnder = {
                   hgData: `小|${convertHandicapToFraction(hgUnderTotal)}|${hgUnderOdds.toFixed(2)}|${hgItem.underbetStr || ''}`,
                   obData: `大|${convertHandicapToFraction(obItem.overTotal ? obItem.overTotal.replace(/^大\s*/, '').trim() : '')}|${obOverOdds.toFixed(2)}`,
              totalOdds: match.hgUnderObOverTotal
            };
          }
             }
          }
        }
        
        // 检查半场让球水位 - 使用最佳匹配结果（使用预转换的数值）
        const maxHalfHandicapWater = Math.max(hgHalfHomeObHalfAwayTotal, hgHalfAwayObHalfHomeTotal);
        if (maxHalfHandicapWater >= waterLevelThreshold) {
          // 直接从匹配结果中获取最佳匹配的盘口数据（包含投注字符串）
          const bestMatch = match.allHalfTimeHandicapMatches && match.allHalfTimeHandicapMatches.length > 0 ? match.allHalfTimeHandicapMatches[0] : null;

          if (bestMatch && bestMatch.hgItem && bestMatch.obItem) {
            const hgItem = bestMatch.hgItem;
            const obItem = bestMatch.obItem;
            const hgHalfHomeOdds = parseFloat(hgItem.homeOdds) || 0;
            const hgHalfAwayOdds = parseFloat(hgItem.awayOdds) || 0;
            const obHalfHomeOdds = parseFloat(obItem.homeOdds) || 0;
            const obHalfAwayOdds = parseFloat(obItem.awayOdds) || 0;

            // 选择水位更高的方向（使用预转换的数值）
            if (hgHalfHomeObHalfAwayTotal >= hgHalfAwayObHalfHomeTotal) {
              // 主客方向水位更高
              if (isOddsInFilterRange(hgHalfHomeOdds, true) && isOddsInFilterRange(obHalfAwayOdds, false)) {
            matchGroups[matchKey].halfTimeHandicap = {
                  hgData: `主|${convertHandicapToFraction(hgItem.homeHandicap)}|${hgHalfHomeOdds.toFixed(2)}|${hgItem.homebetStr || ''}`,
                  obData: `客|${convertHandicapToFraction(obItem.awayHandicap)}|${obHalfAwayOdds.toFixed(2)}`,
              totalOdds: match.hgHalfHomeObHalfAwayTotal
            };
          }
            } else {
              // 客主方向水位更高
              if (isOddsInFilterRange(hgHalfAwayOdds, true) && isOddsInFilterRange(obHalfHomeOdds, false)) {
            matchGroups[matchKey].halfTimeHandicap = {
                  hgData: `客|${convertHandicapToFraction(hgItem.awayHandicap)}|${hgHalfAwayOdds.toFixed(2)}|${hgItem.awaybetStr || ''}`,
                  obData: `主|${convertHandicapToFraction(obItem.homeHandicap)}|${obHalfHomeOdds.toFixed(2)}`,
              totalOdds: match.hgHalfAwayObHalfHomeTotal
            };
          }
            }
          }
        }
        
        // 检查半场大小球水位 - 使用最佳匹配结果（使用预转换的数值）
        const maxHalfOverUnderWater = Math.max(hgHalfTimeOverObUnderTotal, hgHalfTimeUnderObOverTotal);
        if (maxHalfOverUnderWater >= waterLevelThreshold) {
          // 直接从匹配结果中获取最佳匹配的盘口数据（包含投注字符串）
          const bestMatch = match.allHalfTimeOverUnderMatches && match.allHalfTimeOverUnderMatches.length > 0 ? match.allHalfTimeOverUnderMatches[0] : null;

          if (bestMatch && bestMatch.hgItem && bestMatch.obItem) {
            const hgItem = bestMatch.hgItem;
            const obItem = bestMatch.obItem;
            const hgHalfOverOdds = parseFloat(hgItem.overOdds) || 0;
            const hgHalfUnderOdds = parseFloat(hgItem.underOdds) || 0;
            const obHalfOverOdds = parseFloat(obItem.overOdds) || 0;
            const obHalfUnderOdds = parseFloat(obItem.underOdds) || 0;

            // 选择水位更高的方向（使用预转换的数值）
            if (hgHalfTimeOverObUnderTotal >= hgHalfTimeUnderObOverTotal) {
              // 大小方向水位更高
              if (isOddsInFilterRange(hgHalfOverOdds, true) && isOddsInFilterRange(obHalfUnderOdds, false)) {
            const hgOverTotal = hgItem.overTotal ? hgItem.overTotal.replace(/^大\s*/, '').trim() : '';
            matchGroups[matchKey].halfTimeOverUnder = {
                  hgData: `大|${convertHandicapToFraction(hgOverTotal)}|${hgHalfOverOdds.toFixed(2)}|${hgItem.overbetStr || ''}`,
                  obData: `小|${convertHandicapToFraction(obItem.underTotal ? obItem.underTotal.replace(/^小\s*/, '').trim() : '')}|${obHalfUnderOdds.toFixed(2)}`,
              totalOdds: match.hgHalfTimeOverObUnderTotal
            };
          }
            } else {
              // 小大方向水位更高
              if (isOddsInFilterRange(hgHalfUnderOdds, true) && isOddsInFilterRange(obHalfOverOdds, false)) {
            const hgUnderTotal = hgItem.underTotal ? hgItem.underTotal.replace(/^小\s*/, '').trim() : '';
            matchGroups[matchKey].halfTimeOverUnder = {
                  hgData: `小|${convertHandicapToFraction(hgUnderTotal)}|${hgHalfUnderOdds.toFixed(2)}|${hgItem.underbetStr || ''}`,
                  obData: `大|${convertHandicapToFraction(obItem.overTotal ? obItem.overTotal.replace(/^大\s*/, '').trim() : '')}|${obHalfOverOdds.toFixed(2)}`,
              totalOdds: match.hgHalfTimeUnderObOverTotal
            };
              }
            }
          }
        }
      });
      
      // 将分组后的数据转换为数组
      for (const key in matchGroups) {
        // 只添加至少有一个有效盘口的赛事
        if (matchGroups[key].fullTimeHandicap.totalOdds || 
            matchGroups[key].fullTimeOverUnder.totalOdds || 
            matchGroups[key].halfTimeHandicap.totalOdds || 
            matchGroups[key].halfTimeOverUnder.totalOdds) {
          
          // 创建比赛ID
          const matchId = `${matchGroups[key].league}_${matchGroups[key].teams.home}_${matchGroups[key].teams.away}`;
          
          // 检查这个提示是否是新出现的
          const isNewAlert = !currentAlertIds.has(matchId);
          
          // 添加时间戳属性用于各盘口类型计时
          // 如果是新提示或者之前没有这个盘口的提示，重置计时
          if (matchGroups[key].fullTimeHandicap.totalOdds) {
            const fthKey = `${matchId}_fth`;
            if (isNewAlert || !timerStorage[fthKey]) {
              timerStorage[fthKey] = new Date().getTime();
            }
          }
          
          if (matchGroups[key].fullTimeOverUnder.totalOdds) {
            const ftouKey = `${matchId}_ftou`;
            if (isNewAlert || !timerStorage[ftouKey]) {
              timerStorage[ftouKey] = new Date().getTime();
            }
          }
          
          if (matchGroups[key].halfTimeHandicap.totalOdds) {
            const hthKey = `${matchId}_hth`;
            if (isNewAlert || !timerStorage[hthKey]) {
              timerStorage[hthKey] = new Date().getTime();
            }
          }
          
          if (matchGroups[key].halfTimeOverUnder.totalOdds) {
            const htouKey = `${matchId}_htou`;
            if (isNewAlert || !timerStorage[htouKey]) {
              timerStorage[htouKey] = new Date().getTime();
            }
          }
          
          // 检查是否有高亮提示，如果有，记录到日志
          const hasHighlight = 
  (parseFloat(matchGroups[key].fullTimeHandicap.totalOdds) >= highlightThreshold) ||
  (parseFloat(matchGroups[key].fullTimeOverUnder.totalOdds) >= highlightThreshold) ||
  (parseFloat(matchGroups[key].halfTimeHandicap.totalOdds) >= highlightThreshold) ||
  (parseFloat(matchGroups[key].halfTimeOverUnder.totalOdds) >= highlightThreshold);
          
          // 检查是否已存在于活跃提示中
          const existingAlertIndex = activeAlerts.findIndex(alert => 
            alert.league === matchGroups[key].league && 
            alert.teams.home === matchGroups[key].teams.home && 
            alert.teams.away === matchGroups[key].teams.away
          );
          
          // 如果是新的高亮提示，添加到日志
          if (hasHighlight && existingAlertIndex === -1) {
            // 记录新出现的高亮提示到日志
            addAlertToLog(matchGroups[key], "新出现的高亮提示");
            // 更新日志表格
            updateLogTable();
            // 播放提示音
            playAlertSound();
          } else if (existingAlertIndex !== -1) {
            // 检查是否从非高亮变为高亮
            const existingAlert = activeAlerts[existingAlertIndex];
            const wasHighlight = 
    (parseFloat(existingAlert.fullTimeHandicap.totalOdds) >= highlightThreshold) ||
    (parseFloat(existingAlert.fullTimeOverUnder.totalOdds) >= highlightThreshold) ||
    (parseFloat(existingAlert.halfTimeHandicap.totalOdds) >= highlightThreshold) ||
    (parseFloat(existingAlert.halfTimeOverUnder.totalOdds) >= highlightThreshold);
            
            if (hasHighlight && !wasHighlight) {
              // 记录从非高亮变为高亮的提示到日志
              addAlertToLog(matchGroups[key], "变为高亮提示");
              // 更新日志表格
              updateLogTable();
              // 播放提示音
              playAlertSound();
            }
          }
          
          alerts.push(matchGroups[key]);
        }
      }
      
      // 完全替换活跃提示列表
      activeAlerts = [];
        
      // 将分组后的数据转换为数组，添加到活跃提示列表
      if (alerts.length > 0) {
        // 按水位从高到低排序
        alerts.sort((a, b) => {
          // 计算每个alert的最高水位值
          const maxOddsA = Math.max(
            parseFloat(a.fullTimeHandicap.totalOdds) || 0,
            parseFloat(a.fullTimeOverUnder.totalOdds) || 0,
            parseFloat(a.halfTimeHandicap.totalOdds) || 0,
            parseFloat(a.halfTimeOverUnder.totalOdds) || 0
          );
          
          const maxOddsB = Math.max(
            parseFloat(b.fullTimeHandicap.totalOdds) || 0,
            parseFloat(b.fullTimeOverUnder.totalOdds) || 0,
            parseFloat(b.halfTimeHandicap.totalOdds) || 0,
            parseFloat(b.halfTimeOverUnder.totalOdds) || 0
          );
          
          // 从高到低排序
          return maxOddsB - maxOddsA;
        });
        
        activeAlerts = alerts;
        
        // 检查之前的高亮提示是否在新的提示列表中消失
        currentHighlightAlerts.forEach(oldAlert => {
          const matchId = `${oldAlert.league}_${oldAlert.teams.home}_${oldAlert.teams.away}`;
          
          // 在新的提示列表中查找对应的提示
          const newAlert = activeAlerts.find(alert => 
            alert.league === oldAlert.league && 
            alert.teams.home === oldAlert.teams.home && 
            alert.teams.away === oldAlert.teams.away
          );
          
          // 如果找不到对应的提示，或者提示不再是高亮状态，记录到日志
          if (!newAlert) {
            // 完全消失的提示
            addAlertToLog(oldAlert, "提示完全消失");
            updateLogTable();
          } else {
            // 检查各个盘口是否从高亮变为非高亮
            const types = [
              { name: 'fullTimeHandicap', display: '全场让球' },
              { name: 'fullTimeOverUnder', display: '全场大小' },
              { name: 'halfTimeHandicap', display: '半场让球' },
              { name: 'halfTimeOverUnder', display: '半场大小' }
            ];
            
            types.forEach(type => {
              const oldOdds = parseFloat(oldAlert[type.name].totalOdds);
              const newOdds = parseFloat(newAlert[type.name].totalOdds);
              
              // 如果旧的是高亮，但新的不是高亮或者不存在
      if (oldOdds >= highlightThreshold && (newOdds < highlightThreshold || !newOdds)) {
                // 创建一个只包含这个盘口的提示对象
                const singleTypeAlert = {
                  league: oldAlert.league,
                  matchTime: oldAlert.matchTime,
                  teams: oldAlert.teams,
                  hgTeams: oldAlert.hgTeams,
                  obTeams: oldAlert.obTeams,
                  alertTime: oldAlert.alertTime
                };
                
                // 只添加变化的盘口
                singleTypeAlert[type.name] = oldAlert[type.name];
                
                // 记录到日志
                addAlertToLog(singleTypeAlert, `${type.display}高亮消失`);
                updateLogTable();
              }
            });
          }
        });
        
        // 更新提示日志表格
        const alertTableBody = document.getElementById('alertTableBody');
        alertTableBody.innerHTML = '';

        // 标记计时器缓存无效，需要重新获取
        timerCacheValid = false;
      
      // 添加每一行提示
      activeAlerts.forEach((alert, index) => {
        // 创建比赛ID
        const matchId = `${alert.league}_${alert.teams.home}_${alert.teams.away}`;

        // 预转换所有数值，避免重复parseFloat
        const fthOdds = parseFloat(alert.fullTimeHandicap.totalOdds) || 0;
        const ftouOdds = parseFloat(alert.fullTimeOverUnder.totalOdds) || 0;
        const hthOdds = parseFloat(alert.halfTimeHandicap.totalOdds) || 0;
        const htouOdds = parseFloat(alert.halfTimeOverUnder.totalOdds) || 0;

        // 预计算阈值比较结果
        const fthMeetsThreshold = fthOdds >= waterLevelThreshold;
        const ftouMeetsThreshold = ftouOdds >= waterLevelThreshold;
        const hthMeetsThreshold = hthOdds >= waterLevelThreshold;
        const htouMeetsThreshold = htouOdds >= waterLevelThreshold;

        const fthMeetsHighlight = fthOdds >= highlightThreshold;
        const ftouMeetsHighlight = ftouOdds >= highlightThreshold;
        const hthMeetsHighlight = hthOdds >= highlightThreshold;
        const htouMeetsHighlight = htouOdds >= highlightThreshold;

        // 判断哪个盘口的水位最高
        const maxOdds = Math.max(fthOdds, ftouOdds, hthOdds, htouOdds);
        const isFthHighest = fthOdds === maxOdds;
        const isFtouHighest = ftouOdds === maxOdds;
        const isHthHighest = hthOdds === maxOdds;
        const isHtouHighest = htouOdds === maxOdds;

        // 预解析所有数据字符串
        const fthHgParts = alert.fullTimeHandicap.hgData ? alert.fullTimeHandicap.hgData.split('|') : ['', '', ''];
        const fthObParts = alert.fullTimeHandicap.obData ? alert.fullTimeHandicap.obData.split('|') : ['', '', ''];
        const ftouHgParts = alert.fullTimeOverUnder.hgData ? alert.fullTimeOverUnder.hgData.split('|') : ['', '', ''];
        const ftouObParts = alert.fullTimeOverUnder.obData ? alert.fullTimeOverUnder.obData.split('|') : ['', '', ''];
        const hthHgParts = alert.halfTimeHandicap.hgData ? alert.halfTimeHandicap.hgData.split('|') : ['', '', ''];
        const hthObParts = alert.halfTimeHandicap.obData ? alert.halfTimeHandicap.obData.split('|') : ['', '', ''];
        const htouHgParts = alert.halfTimeOverUnder.hgData ? alert.halfTimeOverUnder.hgData.split('|') : ['', '', ''];
        const htouObParts = alert.halfTimeOverUnder.obData ? alert.halfTimeOverUnder.obData.split('|') : ['', '', ''];

        // 创建比赛信息行
        const infoRow = document.createElement('tr');
        infoRow.style.backgroundColor = '#003366';

        // 设置比赛信息行内容
        infoRow.innerHTML = `
          <td colspan="4" style="padding: 5px 8px; font-size: 12px; font-weight: normal; border: none; border-top: 3px solid #003366; border-bottom: 1px solid #003366; text-align: left; color: white; width: 100%; background-color: #003366;">
            <div style="display: flex; justify-content: flex-start; align-items: center; width: 100%;">
              <div style="display: flex; justify-content: flex-start; align-items: center; gap: 15px; width: 70%; text-align: left;">
                <div>
                  <span style="color: gold; font-weight: bold;">${alert.teams.home}</span>|
                  <span style="color: white;">${alert.obTeams.home}</span>
                </div>
                <div style="color: white;">vs</div>
                <div>
                  <span style="color: gold; font-weight: bold;">${alert.teams.away}</span>|
                  <span style="color: white;">${alert.obTeams.away}</span>
                </div>
              </div>
              <span style="color: green; font-weight: bold; width: 30%; text-align: left;">${alert.league}</span>
            </div>
          </td>
        `;

        // 创建数据行
        const row = document.createElement('tr');
        // 设置数据行的背景颜色与比赛信息行一致
        row.style.backgroundColor = index % 2 === 0 ? '#f8f8f8' : 'white';

        // 设置行内容（使用预解析的数据和预计算的条件）
        row.innerHTML = `
                    <td style="text-align: center; font-size: 13px; font-weight: normal; width: 25%; padding: 0; background-color: #003366; border-left: 1px dashed lightblue; border-right: 1px dashed lightblue; border-bottom: ${fthMeetsHighlight ? '3px solid gold' : '1px solid #003366'};">
            <div style="display: grid; grid-template-columns: 15% 30% 30% 25%; width: 100%; background-color: #003366;">
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: gold; background-color: #003366; padding: 0px;">
                ${fthHgParts[0]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${fthHgParts[1]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${fthHgParts[2]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${fthMeetsThreshold ? `<span style="color: ${isFthHighest ? 'gold' : 'white'}; font-weight: ${isFthHighest ? 'bold' : 'normal'};">${alert.fullTimeHandicap.totalOdds}</span>` : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: gold; background-color: #003366; padding: 0px;">
                ${fthObParts[0]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${fthObParts[1]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${fthObParts[2]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${fthMeetsThreshold ? `<span class=\"timer\" data-type=\"fth\" data-id=\"${matchId}\">00:00</span>` : ''}
              </div>
            </div>
          </td>
                    <td style="text-align: center; font-size: 13px; font-weight: normal; width: 25%; padding: 0; background-color: #003366; border-left: 1px dashed lightblue; border-right: 1px dashed lightblue; border-bottom: ${ftouMeetsHighlight ? '3px solid gold' : '1px solid #003366'};">
            <div style="display: grid; grid-template-columns: 15% 30% 30% 25%; width: 100%; background-color: #003366;">
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: gold; background-color: #003366; padding: 0px;">
                ${ftouHgParts[0]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${ftouHgParts[1]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${ftouHgParts[2]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${ftouMeetsThreshold ? `<span style="color: ${isFtouHighest ? 'gold' : 'white'}; font-weight: ${isFtouHighest ? 'bold' : 'normal'};">${alert.fullTimeOverUnder.totalOdds}</span>` : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: gold; background-color: #003366; padding: 0px;">
                ${ftouObParts[0]}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.fullTimeOverUnder.obData ? alert.fullTimeOverUnder.obData.split('|')[1] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.fullTimeOverUnder.obData ? alert.fullTimeOverUnder.obData.split('|')[2] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${parseFloat(alert.fullTimeOverUnder.totalOdds) >= waterLevelThreshold ? `<span class=\"timer\" data-type=\"ftou\" data-id=\"${matchId}\">00:00</span>` : ''}
              </div>
            </div>
          </td>
                    <td style="text-align: center; font-size: 13px; font-weight: normal; width: 25%; padding: 0; background-color: #003366; border-left: 1px dashed lightblue; border-right: 1px dashed lightblue; border-bottom: ${parseFloat(alert.halfTimeHandicap.totalOdds) >= highlightThreshold ? '3px solid gold' : '1px solid #003366'};">
            <div style="display: grid; grid-template-columns: 15% 30% 30% 25%; width: 100%; background-color: #003366;">
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: gold; background-color: #003366; padding: 0px;">
                ${alert.halfTimeHandicap.hgData ? alert.halfTimeHandicap.hgData.split('|')[0] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.halfTimeHandicap.hgData ? alert.halfTimeHandicap.hgData.split('|')[1] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.halfTimeHandicap.hgData ? alert.halfTimeHandicap.hgData.split('|')[2] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${parseFloat(alert.halfTimeHandicap.totalOdds) >= waterLevelThreshold ? `<span style="color: ${isHthHighest ? 'gold' : 'white'}; font-weight: ${isHthHighest ? 'bold' : 'normal'};">${alert.halfTimeHandicap.totalOdds}</span>` : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: gold; background-color: #003366; padding: 0px;">
                ${alert.halfTimeHandicap.obData ? alert.halfTimeHandicap.obData.split('|')[0] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.halfTimeHandicap.obData ? alert.halfTimeHandicap.obData.split('|')[1] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.halfTimeHandicap.obData ? alert.halfTimeHandicap.obData.split('|')[2] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${parseFloat(alert.halfTimeHandicap.totalOdds) >= waterLevelThreshold ? `<span class=\"timer\" data-type=\"hth\" data-id=\"${matchId}\">00:00</span>` : ''}
              </div>
            </div>
          </td>
                    <td style="text-align: center; font-size: 13px; font-weight: normal; width: 25%; padding: 0; background-color: #003366; border-left: 1px dashed lightblue; border-right: 1px dashed lightblue; border-bottom: ${parseFloat(alert.halfTimeOverUnder.totalOdds) >= highlightThreshold ? '3px solid gold' : '1px solid #003366'};">
            <div style="display: grid; grid-template-columns: 15% 30% 30% 25%; width: 100%; background-color: #003366;">
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: gold; background-color: #003366; padding: 0px;">
                ${alert.halfTimeOverUnder.hgData ? alert.halfTimeOverUnder.hgData.split('|')[0] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.halfTimeOverUnder.hgData ? alert.halfTimeOverUnder.hgData.split('|')[1] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.halfTimeOverUnder.hgData ? alert.halfTimeOverUnder.hgData.split('|')[2] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${parseFloat(alert.halfTimeOverUnder.totalOdds) >= waterLevelThreshold ? `<span style="color: ${isHtouHighest ? 'gold' : 'white'}; font-weight: ${isHtouHighest ? 'bold' : 'normal'};">${alert.halfTimeOverUnder.totalOdds}</span>` : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: gold; background-color: #003366; padding: 0px;">
                ${alert.halfTimeOverUnder.obData ? alert.halfTimeOverUnder.obData.split('|')[0] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.halfTimeOverUnder.obData ? alert.halfTimeOverUnder.obData.split('|')[1] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${alert.halfTimeOverUnder.obData ? alert.halfTimeOverUnder.obData.split('|')[2] : ''}
              </div>
              <div style="font-size: 13px; font-weight: normal; line-height: 1.2; text-align: center; color: white; background-color: #003366; padding: 0px;">
                ${parseFloat(alert.halfTimeOverUnder.totalOdds) >= waterLevelThreshold ? `<span class=\"timer\" data-type=\"htou\" data-id=\"${matchId}\">00:00</span>` : ''}
              </div>
            </div>
          </td>
        `;
        
        // 添加两行到表格：先添加比赛信息行，再添加数据行
        alertTableBody.appendChild(infoRow);
        alertTableBody.appendChild(row);
      });
      
      // 立即更新计时器显示
      updateAllTimers();
      
      // 更新JSON数据输入框
      updateAlertJsonData(activeAlerts);
      } else {
        // 如果没有活跃提示，显示暂无水位提示
        const alertTableBody = document.getElementById('alertTableBody');
        alertTableBody.innerHTML = '';
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="4" style="text-align: center; padding: 8px; font-size: 13px; font-weight: normal; color: #666; border: 1px solid #ddd;">暂无水位提示</td>';
        alertTableBody.appendChild(row);
        
        // 清空JSON数据输入框
        updateAlertJsonData([]);
      }
    }
    
    
    // 更新相似度阈值
    function updateSimilarityThreshold() {
      const thresholdInput = document.getElementById('similarityThreshold');
      const newThreshold = parseInt(thresholdInput.value) / 100; // 转换为0-1之间的小数
      
      if (!isNaN(newThreshold) && newThreshold >= 0 && newThreshold <= 1) {
        similarityThreshold = newThreshold;
        console.log(`相似度阈值已更新为: ${newThreshold.toFixed(2)} (${Math.round(newThreshold * 100)}%)`);
        
        // 如果已有数据，重新执行匹配
        if (hgData && obData) {
          if (matchWorker) {
            // 使用Web Worker并行处理
            matchWorker.postMessage({
              type: 'matchGames',
              hgData: hgData,
              obData: obData,
              similarityThreshold: similarityThreshold
            });
          } else {
            // 回退到单线程处理
            matchGames();
          }
        }
      } else {
        alert('请输入0-100之间的有效数值');
        thresholdInput.value = Math.round(similarityThreshold * 100);
      }
    }
    
    // 更新水位阈值
    function updateWaterLevelThresholds() {
      const thresholdInput = document.getElementById('waterLevelThreshold');
      const highlightInput = document.getElementById('highlightThreshold');
      const newThreshold = parseFloat(thresholdInput.value);
      const newHighlightThreshold = parseFloat(highlightInput.value);
      
      let isValid = true;
      let message = '';
      
      // 验证显示阈值
      if (isNaN(newThreshold)) {
        isValid = false;
        message += '请输入有效的显示阈值\n';
      }
      
      // 验证高亮阈值
      if (isNaN(newHighlightThreshold)) {
        isValid = false;
        message += '请输入有效的高亮阈值\n';
      }
      
      if (isValid) {
        waterLevelThreshold = newThreshold;
        highlightThreshold = newHighlightThreshold;
        console.log(`水位阈值已更新为: 显示>${waterLevelThreshold.toFixed(2)}, 高亮>${highlightThreshold.toFixed(2)}`);
        
        // 如果已有匹配结果，重新准备提示数据
        if (fullMatchResults.length > 0) {
          // 应用联赛过滤器获取过滤后的比赛
          const filteredResults = fullMatchResults.filter(match => {
            const league = match.hg.league || '';
            return availableLeagues[league] === true;
          });
          
          // 重新准备提示数据
          prepareAndUpdateAlerts(filteredResults);
        }
      } else {
        alert(message);
        thresholdInput.value = waterLevelThreshold.toFixed(2);
        highlightInput.value = highlightThreshold.toFixed(2);
      }
    }
    
    // 更新赔率过滤设置
    function updateOddsFilter() {
      const hgMinInput = document.getElementById('hgMinOdds');
      const hgMaxInput = document.getElementById('hgMaxOdds');
      const obMinInput = document.getElementById('obMinOdds');
      const obMaxInput = document.getElementById('obMaxOdds');
      
      const newHgMin = parseFloat(hgMinInput.value);
      const newHgMax = parseFloat(hgMaxInput.value);
      const newObMin = parseFloat(obMinInput.value);
      const newObMax = parseFloat(obMaxInput.value);
      
      let isValid = true;
      let errorMessage = '';
      
      // 验证HG赔率范围
      if (isNaN(newHgMin) || isNaN(newHgMax) || newHgMin < 0 || newHgMax <= newHgMin) {
        isValid = false;
        errorMessage += 'HG赔率范围无效（最小值 >= 0，最大值 > 最小值）\n';
      }
      
      // 验证OB赔率范围
      if (isNaN(newObMin) || isNaN(newObMax) || newObMin < 0 || newObMax <= newObMin) {
        isValid = false;
        errorMessage += 'OB赔率范围无效（最小值 >= 0，最大值 > 最小值）';
      }
      
      if (isValid) {
        hgMinOddsFilter = newHgMin;
        hgMaxOddsFilter = newHgMax;
        obMinOddsFilter = newObMin;
        obMaxOddsFilter = newObMax;
        
        console.log(`赔率过滤已更新: HG(${hgMinOddsFilter}-${hgMaxOddsFilter}), OB(${obMinOddsFilter}-${obMaxOddsFilter})`);
        
        // 如果已有数据，重新执行匹配和过滤
        if (hgData && obData) {
          if (matchWorker) {
            // 使用Web Worker并行处理
            matchWorker.postMessage({
              type: 'matchGames',
              hgData: hgData,
              obData: obData,
              similarityThreshold: similarityThreshold
            });
          } else {
            // 回退到单线程处理
            matchGames();
          }
        }
      } else {
        alert(errorMessage);
        // 还原输入框的值
        hgMinInput.value = hgMinOddsFilter;
        hgMaxInput.value = hgMaxOddsFilter;
        obMinInput.value = obMinOddsFilter;
        obMaxInput.value = obMaxOddsFilter;
      }
    }
    
    // 检查赔率是否在过滤范围内
    function isOddsInFilterRange(odds, isHG) {
      const oddsValue = parseFloat(odds);
      if (isHG) {
        return oddsValue >= hgMinOddsFilter && oddsValue <= hgMaxOddsFilter;
      } else {
        return oddsValue >= obMinOddsFilter && oddsValue <= obMaxOddsFilter;
      }
    }
    
    // 更新联赛过滤UI，显示所有可用联赛的复选框
    function updateLeagueFiltersUI() {
      const leagueFiltersContainer = document.getElementById('leagueFilters');
      
      // 按字母顺序排序联赛名称
      const sortedLeagues = Object.keys(availableLeagues).sort();
      
      // 检查是否需要完全重建UI
      // 如果当前的复选框数量与联赛数量不符，或存在新的联赛，则重建UI
      let needRebuild = false;
      const existingCheckboxes = leagueFiltersContainer.querySelectorAll('input[type="checkbox"]');
      
      if (existingCheckboxes.length !== sortedLeagues.length) {
        needRebuild = true;
      } else {
        // 检查是否所有联赛都已有对应复选框
        for (const league of sortedLeagues) {
          const safeId = `league-${league.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
          if (!document.getElementById(safeId)) {
            needRebuild = true;
            break;
          }
        }
      }
      
      // 如果不需要重建，只更新复选框状态
      if (!needRebuild && existingCheckboxes.length > 0) {
        existingCheckboxes.forEach(checkbox => {
          // 从ID提取联赛名
          const idPrefix = 'league-';
          if (checkbox.id.startsWith(idPrefix)) {
            // 尝试找到对应的联赛
            for (const league of sortedLeagues) {
              const safeId = `league-${league.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
              if (checkbox.id === safeId) {
                // 只更新复选框状态，不重建
                checkbox.checked = availableLeagues[league];
                break;
              }
            }
          }
        });
        return;
      }
      
      // 需要重建UI时，清空容器
      leagueFiltersContainer.innerHTML = '';
      
      // 为每个联赛创建一个复选框
      sortedLeagues.forEach(league => {
        const isChecked = availableLeagues[league];
        
        // 创建包含复选框和标签的div
        const div = document.createElement('div');
        div.style.display = 'flex';
        div.style.alignItems = 'center';
        div.style.padding = '2px 5px';
        div.style.backgroundColor = '#fff';
        div.style.border = '1px solid #e0e0e0';
        div.style.borderRadius = '4px';
        div.style.cursor = 'pointer'; // 添加指针样式
        div.dataset.league = league; // 存储联赛名称，便于后续查找
        
        // 创建复选框
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `league-${league.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
        checkbox.checked = isChecked;
        checkbox.style.cursor = 'pointer'; // 添加指针样式
        checkbox.style.pointerEvents = 'auto'; // 确保事件不被阻止
        
        // 确保复选框可点击
        checkbox.addEventListener('click', function(e) {
          e.stopPropagation(); // 阻止事件冒泡
        }, true);
        
        checkbox.addEventListener('change', function() {
          // 更新联赛选中状态
          availableLeagues[league] = this.checked;
          
          // 应用过滤器
          applyLeagueFilters();
        }, true);
        
        // 创建标签
        const label = document.createElement('label');
        label.htmlFor = checkbox.id; // 确保使用相同的ID
        label.textContent = league;
        label.style.marginLeft = '5px';
        label.style.fontSize = '12px';
        label.style.whiteSpace = 'nowrap';
        label.style.cursor = 'pointer'; // 添加指针样式
        label.style.userSelect = 'none'; // 防止文本被选中
        label.style.pointerEvents = 'auto'; // 确保事件不被阻止
        
        // 整个div的点击事件也可以切换复选框
        div.addEventListener('click', function(e) {
          if (e.target !== checkbox) { // 如果点击的不是复选框本身
            checkbox.checked = !checkbox.checked;
            
            // 手动触发change事件
            const changeEvent = new Event('change');
            checkbox.dispatchEvent(changeEvent);
          }
        }, true);
        
        // 添加到div
        div.appendChild(checkbox);
        div.appendChild(label);
        
        // 添加到容器
        leagueFiltersContainer.appendChild(div);
      });
    }
    
    // 全选或全不选所有联赛
    function selectAllLeagues(select) {
      // 更新所有联赛的选中状态
      Object.keys(availableLeagues).forEach(league => {
        availableLeagues[league] = select;
      });
      
      // 更新UI
      updateLeagueFiltersUI();
      
      // 应用过滤器
      applyLeagueFilters();
    }
    
    // 应用联赛过滤器
    function applyLeagueFilters() {
      // 如果已有数据，应用过滤
      if (fullMatchResults && fullMatchResults.length > 0) {
        // 过滤符合条件的比赛
        const filteredResults = fullMatchResults.filter(match => {
          const league = match.hg.league || '';
          return availableLeagues[league] === true;
        });
        
        // 处理被过滤掉的联赛相关的提示
        handleFilteredLeagueAlerts(filteredResults);
        
        // 更新表格显示
        updateMatchTableUI(filteredResults);
        
        // 重新准备提示数据
        prepareAndUpdateAlerts(filteredResults);
      }
    }
    
    // 仅更新匹配表格UI，不更新提示日志
    function updateMatchTableUI(matchResults) {
      // 获取当前所有显示的比赛ID和盘口类型
      const currentMatchIds = new Set();
      document.querySelectorAll('.timer').forEach(timer => {
        const type = timer.getAttribute('data-type');
        const id = timer.getAttribute('data-id');
        if (type && id) {
          currentMatchIds.add(`${id}_${type}`);
        }
      });
      
      // 根据当前活动的选项卡选择要更新的表格
      let tableBody;
      
      switch (activeTab) {
        case 'fulltime-handicap':
          tableBody = document.getElementById('fulltimeHandicapTableBody');
          break;
        case 'fulltime-overunder':
          tableBody = document.getElementById('fulltimeOverunderTableBody');
          break;
        case 'halftime-handicap':
          tableBody = document.getElementById('halftimeHandicapTableBody');
          break;
        case 'halftime-overunder':
          tableBody = document.getElementById('halftimeOverunderTableBody');
          break;
        default:
          tableBody = document.getElementById('fulltimeHandicapTableBody');
      }
      
      // 清空表格
      tableBody.innerHTML = '';
      
      // 如果没有符合条件的比赛
      if (matchResults.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="10" style="text-align: center; padding: 10px;">没有符合条件的比赛</td>';
        tableBody.appendChild(row);
        return;
      }
      
      // 添加每一行 - 显示所有匹配的盘口
      let globalIndex = 1; // 全局序号计数器
      
      matchResults.forEach((match) => {
        // 计算相似度百分比
        const totalScorePercent = Math.round(match.totalScore * 100);
        
        // 获取比分信息
        const hgScore = match.hg.score || '0-0';
        const obScore = match.ob.score || '0-0';
        
        // 创建计时器ID
        const matchId = `${match.hg.league}_${match.hg.teams.home}_${match.hg.teams.away}`;
        
        // 根据当前选项卡获取对应的所有匹配数据
        let allMatches = [];
        switch (activeTab) {
          case 'fulltime-handicap':
            allMatches = match.allFullTimeHandicapMatches || [];
            break;
          case 'fulltime-overunder':
            allMatches = match.allFullTimeOverUnderMatches || [];
            break;
          case 'halftime-handicap':
            allMatches = match.allHalfTimeHandicapMatches || [];
            break;
          case 'halftime-overunder':
            allMatches = match.allHalfTimeOverUnderMatches || [];
            break;
          default:
            allMatches = match.allFullTimeHandicapMatches || [];
        }
        
        // 如果没有匹配的盘口，显示无匹配信息
        if (allMatches.length === 0) {
          const hgRow = document.createElement('tr');
          const obRow = document.createElement('tr');
          
          hgRow.innerHTML = `
            <td rowspan="2">${globalIndex++}</td>
            <td>HG</td>
            <td>${match.hg.dateTime || '未知'}</td>
            <td>${hgScore}</td>
            <td>${match.hg.league || '未知'}</td>
            <td>${match.hg.teams.home}</td>
            <td>${match.hg.teams.away}</td>
            <td style="max-width: 200px; font-size: 11px;">无匹配盘口</td>
            <td rowspan="2" class="water-section">无水位</td>
            <td rowspan="2">${totalScorePercent}%</td>
          `;
          
          obRow.innerHTML = `
            <td>OB</td>
            <td>${match.ob.dateTime || '未知'}</td>
            <td>${obScore}</td>
            <td>${match.ob.league || '未知'}</td>
            <td>${match.ob.teams.home}</td>
            <td>${match.ob.teams.away}</td>
            <td style="max-width: 200px; font-size: 11px;">无匹配盘口</td>
          `;
          
          tableBody.appendChild(hgRow);
          tableBody.appendChild(obRow);
          return;
        }
        
        // 为每个匹配的盘口创建两行（HG行和OB行）
        allMatches.forEach((handicapMatch) => {
        // 根据当前选项卡设置不同的行内容
        switch (activeTab) {
          case 'fulltime-handicap':
            // 创建计时器键
              const fthKey = `${matchId}_fth_${globalIndex}`;
            
            // 检查是否是新出现的比赛或盘口
            const isFthNew = !currentMatchIds.has(fthKey);
            
            // 如果是新的比赛或盘口，重置计时器
            if (isFthNew) {
              timerStorage[fthKey] = new Date().getTime();
            }
              
              // 创建HG行和OB行
              const hgRow = document.createElement('tr');
              const obRow = document.createElement('tr');
            
            // HG行
            hgRow.innerHTML = `
                <td rowspan="2">${globalIndex++}</td>
              <td>HG</td>
          <td>${match.hg.dateTime || '未知'}</td>
              <td>${hgScore}</td>
              <td>${match.hg.league || '未知'}</td>
              <td>${match.hg.teams.home}</td>
              <td>${match.hg.teams.away}</td>
                <td style="max-width: 200px; font-size: 11px;">${handicapMatch.hgLine || '无盘口'}</td>
                <td rowspan="2" class="water-section">主客: ${handicapMatch.homeAwayWater}<br>客主: ${handicapMatch.awayHomeWater}</td>
              <td rowspan="2">${totalScorePercent}%</td>
            `;
            
              // OB行
              obRow.innerHTML = `
              <td>OB</td>
              <td>${match.ob.dateTime || '未知'}</td>
              <td>${obScore}</td>
              <td>${match.ob.league || '未知'}</td>
              <td>${match.ob.teams.home}</td>
              <td>${match.ob.teams.away}</td>
                <td style="max-width: 200px; font-size: 11px;">${handicapMatch.obLine || '无匹配盘口'}</td>
            `;
            
            // 添加行到表格
            tableBody.appendChild(hgRow);
              tableBody.appendChild(obRow);
            break;
            
          case 'fulltime-overunder':
            // 创建计时器键
              const ftouKey = `${matchId}_ftou_${globalIndex}`;
            
            // 检查是否是新出现的比赛或盘口
            const isFtouNew = !currentMatchIds.has(ftouKey);
            
            // 如果是新的比赛或盘口，重置计时器
            if (isFtouNew) {
              timerStorage[ftouKey] = new Date().getTime();
            }
              
              // 创建HG行和OB行
              const hgRowOU = document.createElement('tr');
              const obRowOU = document.createElement('tr');
            
            // HG行
              hgRowOU.innerHTML = `
                <td rowspan="2">${globalIndex++}</td>
              <td>HG</td>
              <td>${match.hg.dateTime || '未知'}</td>
              <td>${hgScore}</td>
              <td>${match.hg.league || '未知'}</td>
              <td>${match.hg.teams.home}</td>
              <td>${match.hg.teams.away}</td>
                <td style="max-width: 200px; font-size: 11px;">${handicapMatch.hgLine || '无盘口'}</td>
                <td rowspan="2" class="water-section">大小: ${handicapMatch.overUnderWater}<br>小大: ${handicapMatch.underOverWater}</td>
              <td rowspan="2">${totalScorePercent}%</td>
            `;
            
              // OB行
              obRowOU.innerHTML = `
              <td>OB</td>
              <td>${match.ob.dateTime || '未知'}</td>
              <td>${obScore}</td>
              <td>${match.ob.league || '未知'}</td>
              <td>${match.ob.teams.home}</td>
              <td>${match.ob.teams.away}</td>
                <td style="max-width: 200px; font-size: 11px;">${handicapMatch.obLine || '无匹配盘口'}</td>
            `;
            
            // 添加行到表格
              tableBody.appendChild(hgRowOU);
              tableBody.appendChild(obRowOU);
            break;
            
          case 'halftime-handicap':
            // 创建计时器键
              const hthKey = `${matchId}_hth_${globalIndex}`;
            
            // 检查是否是新出现的比赛或盘口
            const isHthNew = !currentMatchIds.has(hthKey);
            
            // 如果是新的比赛或盘口，重置计时器
            if (isHthNew) {
              timerStorage[hthKey] = new Date().getTime();
            }
              
              // 创建HG行和OB行
              const hgRowHTH = document.createElement('tr');
              const obRowHTH = document.createElement('tr');
            
            // HG行
              hgRowHTH.innerHTML = `
                <td rowspan="2">${globalIndex++}</td>
              <td>HG</td>
              <td>${match.hg.dateTime || '未知'}</td>
              <td>${hgScore}</td>
              <td>${match.hg.league || '未知'}</td>
              <td>${match.hg.teams.home}</td>
              <td>${match.hg.teams.away}</td>
                <td style="max-width: 200px; font-size: 11px;">${handicapMatch.hgLine || '无盘口'}</td>
                <td rowspan="2" class="water-section">主客: ${handicapMatch.homeAwayWater}<br>客主: ${handicapMatch.awayHomeWater}</td>
              <td rowspan="2">${totalScorePercent}%</td>
            `;
            
              // OB行
            obRowHTH.innerHTML = `
              <td>OB</td>
              <td>${match.ob.dateTime || '未知'}</td>
              <td>${obScore}</td>
              <td>${match.ob.league || '未知'}</td>
              <td>${match.ob.teams.home}</td>
              <td>${match.ob.teams.away}</td>
                <td style="max-width: 200px; font-size: 11px;">${handicapMatch.obLine || '无匹配盘口'}</td>
            `;
            
            // 添加行到表格
              tableBody.appendChild(hgRowHTH);
            tableBody.appendChild(obRowHTH);
            break;
            
          case 'halftime-overunder':
            // 创建计时器键
              const htouKey = `${matchId}_htou_${globalIndex}`;
            
            // 检查是否是新出现的比赛或盘口
            const isHtouNew = !currentMatchIds.has(htouKey);
            
            // 如果是新的比赛或盘口，重置计时器
            if (isHtouNew) {
              timerStorage[htouKey] = new Date().getTime();
            }
              
              // 创建HG行和OB行
              const hgRowHTOU = document.createElement('tr');
              const obRowHTOU = document.createElement('tr');
            
            // HG行
              hgRowHTOU.innerHTML = `
                <td rowspan="2">${globalIndex++}</td>
              <td>HG</td>
              <td>${match.hg.dateTime || '未知'}</td>
              <td>${hgScore}</td>
              <td>${match.hg.league || '未知'}</td>
              <td>${match.hg.teams.home}</td>
              <td>${match.hg.teams.away}</td>
                <td style="max-width: 200px; font-size: 11px;">${handicapMatch.hgLine || '无盘口'}</td>
                <td rowspan="2" class="water-section">大小: ${handicapMatch.overUnderWater}<br>小大: ${handicapMatch.underOverWater}</td>
              <td rowspan="2">${totalScorePercent}%</td>
            `;
            
              // OB行
            obRowHTOU.innerHTML = `
              <td>OB</td>
              <td>${match.ob.dateTime || '未知'}</td>
              <td>${obScore}</td>
              <td>${match.ob.league || '未知'}</td>
              <td>${match.ob.teams.home}</td>
              <td>${match.ob.teams.away}</td>
                <td style="max-width: 200px; font-size: 11px;">${handicapMatch.obLine || '无匹配盘口'}</td>
            `;
            
            // 添加行到表格
              tableBody.appendChild(hgRowHTOU);
            tableBody.appendChild(obRowHTOU);
            break;
        }
        });
      });
      
      // 立即更新计时器显示
      updateAllTimers();
    }
    
    // 计算两个字符串的相似度 (使用最长公共子序列算法)
    function calculateStringSimilarity(a, b) {
      if (!a || !b) return 0;
      if (a === b) return 1;
      
      // 将文本转换为字符数组
      const charListA = getCharList(a);
      const charListB = getCharList(b);
      
      const n = charListA.length;
      const m = charListB.length;
      
      if (n + m <= 0) return 0;
      
      // 计算最长公共子序列长度
      const lcsLength = getMaxLenSubStr(charListA, charListB);
      
      // 相似度 = 最长公共子序列长度 / 较长文本的长度
      return lcsLength / Math.max(n, m);
    }
    
    // 提取赔率值函数 (从嵌套函数移动到全局)
    function extractOddsValue(oddsString, isHome) {
      if (!oddsString) return 0;
      
      try {
        // 如果是对象格式（新的JSON数据结构）
        if (typeof oddsString === 'object') {
          return isHome ? parseFloat(oddsString.homeOdds) || 0 : parseFloat(oddsString.awayOdds) || 0;
        }
        
        // 从让球盘口格式中提取赔率值
        // 例如 "-0.5[1.00];+0.5[0.88]" 或 "+0.5[1.00];-0.5[0.88]"
        const parts = oddsString.split(';');
        if (parts.length !== 2) return 0;
        
        // 提取主队赔率
        const homeOddsPart = parts[0];
        const homeOddsMatch = homeOddsPart.match(/\[([\d\.]+)\]/);
        const homeOdds = homeOddsMatch ? parseFloat(homeOddsMatch[1]) : 0;
        
        // 提取客队赔率
        const awayOddsPart = parts[1];
        const awayOddsMatch = awayOddsPart.match(/\[([\d\.]+)\]/);
        const awayOdds = awayOddsMatch ? parseFloat(awayOddsMatch[1]) : 0;
        
        return isHome ? homeOdds : awayOdds;
      } catch (e) {
        return 0;
      }
    }
    
    // 从盘口中提取让球值函数 (从嵌套函数移动到全局)
    function extractHandicapValue(oddsString) {
      if (!oddsString) return null;
      
      try {
        // 如果是对象格式（新的JSON数据结构）
        if (typeof oddsString === 'object') {
          return {
            home: oddsString.homeHandicap || null,
            away: oddsString.awayHandicap || null,
            homebetStr: oddsString.homebetStr || null,
            awaybetStr: oddsString.awaybetStr || null
          };
        }
        
        // 从让球盘口格式中提取让球值
        // 例如 "-0.5[1.00];+0.5[0.88]" 中提取 "-0.5" 和 "+0.5"
        const parts = oddsString.split(';');
        if (parts.length !== 2) return null;
        
        // 提取主队让球值
        const homeOddsPart = parts[0];
        const homeHandicapMatch = homeOddsPart.match(/^([+-]?\d*\.?\d*)\[/);
        const homeHandicap = homeHandicapMatch ? homeHandicapMatch[1] : null;
        
        // 提取客队让球值
        const awayOddsPart = parts[1];
        const awayHandicapMatch = awayOddsPart.match(/^([+-]?\d*\.?\d*)\[/);
        const awayHandicap = awayHandicapMatch ? awayHandicapMatch[1] : null;
        
        return { home: homeHandicap, away: awayHandicap };
      } catch (e) {
        return null;
      }
    }
    
    // 找到HG和OB平台间所有匹配的盘口 (返回所有匹配组合)
    function findAllMatchingHandicaps(hgFullHandicapArray, obFullHandicapArray) {
      if (!hgFullHandicapArray || !obFullHandicapArray || !Array.isArray(hgFullHandicapArray) || !Array.isArray(obFullHandicapArray)) return [];
      
      let allMatches = [];
      
      // 遍历所有HG盘口
      for (const hgItem of hgFullHandicapArray) {
        const hgHomeHandicap = hgItem.homeHandicap;
        const hgAwayHandicap = hgItem.awayHandicap;
        const hgHomeOdds = parseFloat(hgItem.homeOdds) || 0;
        const hgAwayOdds = parseFloat(hgItem.awayOdds) || 0;
        
        if (!hgHomeHandicap || !hgAwayHandicap) continue;
        
        // 遍历所有OB盘口，寻找让球值匹配的盘口
        for (const obItem of obFullHandicapArray) {
          const obHomeHandicap = obItem.homeHandicap;
          const obAwayHandicap = obItem.awayHandicap;
          const obHomeOdds = parseFloat(obItem.homeOdds) || 0;
          const obAwayOdds = parseFloat(obItem.awayOdds) || 0;
          
          if (!obHomeHandicap || !obAwayHandicap) continue;
          
          // 检查让球值是否匹配
          if (hgHomeHandicap === obHomeHandicap && hgAwayHandicap === obAwayHandicap) {
            // 计算两个方向的水位
            const homeAwayWater = hgHomeOdds * obAwayOdds;
            const awayHomeWater = hgAwayOdds * obHomeOdds;
            const maxWaterLevel = Math.max(homeAwayWater, awayHomeWater);
            
            allMatches.push({
              hgItem: hgItem,
              obItem: obItem,
              hgLine: `${hgHomeHandicap}[${hgHomeOdds}];${hgAwayHandicap}[${hgAwayOdds}]`,
              obLine: `${obHomeHandicap}[${obHomeOdds}];${obAwayHandicap}[${obAwayOdds}]`,
              waterLevel: maxWaterLevel,
              homeAwayWater: homeAwayWater.toFixed(2),
              awayHomeWater: awayHomeWater.toFixed(2)
            });
          }
        }
      }
      
      // 按水位从高到低排序
      allMatches.sort((a, b) => b.waterLevel - a.waterLevel);
      
      return allMatches;
    }

    // 找到HG和OB平台间所有匹配的大小球盘口 (返回所有匹配组合)
    function findAllMatchingOverUnders(hgFullOverUnderArray, obFullOverUnderArray) {
      if (!hgFullOverUnderArray || !obFullOverUnderArray || !Array.isArray(hgFullOverUnderArray) || !Array.isArray(obFullOverUnderArray)) return [];
      
      let allMatches = [];
      
      // 遍历所有HG盘口
      for (const hgItem of hgFullOverUnderArray) {
        // 提取大小球值（去掉"大"和"小"前缀）
        const hgOverTotal = hgItem.overTotal ? hgItem.overTotal.replace(/^大\s*/, '').trim() : '';
        const hgUnderTotal = hgItem.underTotal ? hgItem.underTotal.replace(/^小\s*/, '').trim() : '';
        const hgOverOdds = parseFloat(hgItem.overOdds) || 0;
        const hgUnderOdds = parseFloat(hgItem.underOdds) || 0;
        
        if (!hgOverTotal || !hgUnderTotal) continue;
        
        // 遍历所有OB盘口，寻找大小球值匹配的盘口
        for (const obItem of obFullOverUnderArray) {
          // 提取大小球值（去掉"大"和"小"前缀）
          const obOverTotal = obItem.overTotal ? obItem.overTotal.replace(/^大\s*/, '').trim() : '';
          const obUnderTotal = obItem.underTotal ? obItem.underTotal.replace(/^小\s*/, '').trim() : '';
          const obOverOdds = parseFloat(obItem.overOdds) || 0;
          const obUnderOdds = parseFloat(obItem.underOdds) || 0;
          
          if (!obOverTotal || !obUnderTotal) continue;
          
          // 检查大小球值是否匹配
          if (hgOverTotal === obOverTotal && hgUnderTotal === obUnderTotal) {
            // 计算两个方向的水位
            const overUnderWater = hgOverOdds * obUnderOdds;
            const underOverWater = hgUnderOdds * obOverOdds;
            const maxWaterLevel = Math.max(overUnderWater, underOverWater);
            
            allMatches.push({
              hgItem: hgItem,
              obItem: obItem,
              hgLine: `大${hgOverTotal}[${hgOverOdds}];小${hgUnderTotal}[${hgUnderOdds}]`,
              obLine: `大${obOverTotal}[${obOverOdds}];小${obUnderTotal}[${obUnderOdds}]`,
              waterLevel: maxWaterLevel,
              overUnderWater: overUnderWater.toFixed(2),
              underOverWater: underOverWater.toFixed(2)
            });
          }
        }
      }
      
      // 按水位从高到低排序
      allMatches.sort((a, b) => b.waterLevel - a.waterLevel);
      
      return allMatches;
    }

    // 找到HG和OB平台间最佳匹配的盘口 (支持多盘口匹配) - 保留兼容性
    function findBestMatchingHandicap(hgFullHandicapArray, obFullHandicapArray) {
      const allMatches = findAllMatchingHandicaps(hgFullHandicapArray, obFullHandicapArray);
      return allMatches.length > 0 ? allMatches[0] : { hgLine: "无匹配盘口", obLine: "无匹配盘口", waterLevel: 0 };
    }

    // 保留原有函数以兼容现有代码，但内部使用新的匹配逻辑
    function findMatchingOBHandicap(hgItem, obFullHandicapArray) {
      if (!hgItem || !obFullHandicapArray || !Array.isArray(obFullHandicapArray)) return "无匹配盘口";
      
      // 遍历每个OB盘口，找到让球值完全相同的那个
      for (const obItem of obFullHandicapArray) {
        if (hgItem.homeHandicap === obItem.homeHandicap && hgItem.awayHandicap === obItem.awayHandicap) {
          return `${obItem.homeHandicap}[${obItem.homeOdds}];${obItem.awayHandicap}[${obItem.awayOdds}]`;
        }
      }
      
      // 如果没找到匹配的，返回无匹配盘口
      return "无匹配盘口";
    }
    
    // 从盘口中提取大小球值函数 (从嵌套函数移动到全局)
    function extractOverUnderValue(oddsString) {
      if (!oddsString) return null;
      
      try {
        // 如果是对象格式（新的JSON数据结构）
        if (typeof oddsString === 'object') {
          const overTotal = oddsString.overTotal ? oddsString.overTotal.replace(/^大\s*/, '').trim() : null;
          const underTotal = oddsString.underTotal ? oddsString.underTotal.replace(/^小\s*/, '').trim() : null;
          return {
            over: overTotal,
            under: underTotal,
            overbetStr: oddsString.overbetStr || null,
            underbetStr: oddsString.underbetStr || null
          };
        }
        
        // 从大小球盘口格式中提取大小球值
        // 例如 "大2.5[1.00];小2.5[0.88]" 中提取 "大2.5" 和 "小2.5"
        const parts = oddsString.split(';');
        if (parts.length !== 2) return null;
        
        // 提取大球值
        const overPart = parts[0];
        const overMatch = overPart.match(/大([\d\.]+)\[/);
        const over = overMatch ? overMatch[1].trim() : null;
        
        // 提取小球值
        const underPart = parts[1];
        const underMatch = underPart.match(/小([\d\.]+)\[/);
        const under = underMatch ? underMatch[1].trim() : null;
        
        return { over: over, under: under };
      } catch (e) {
        return null;
      }
    }
    
    // 找到HG和OB平台间所有匹配的大小球盘口 (返回所有匹配组合)
    function findAllMatchingOverUnders(hgFullOverUnderArray, obFullOverUnderArray) {
      if (!hgFullOverUnderArray || !obFullOverUnderArray || !Array.isArray(hgFullOverUnderArray) || !Array.isArray(obFullOverUnderArray)) return [];
      
      let allMatches = [];
      
      // 遍历所有HG盘口
      for (const hgItem of hgFullOverUnderArray) {
        // 提取大小球值（去掉"大"和"小"前缀）
        const hgOverTotal = hgItem.overTotal ? hgItem.overTotal.replace(/^大\s*/, '').trim() : '';
        const hgUnderTotal = hgItem.underTotal ? hgItem.underTotal.replace(/^小\s*/, '').trim() : '';
        const hgOverOdds = parseFloat(hgItem.overOdds) || 0;
        const hgUnderOdds = parseFloat(hgItem.underOdds) || 0;
        
        if (!hgOverTotal || !hgUnderTotal) continue;
        
        // 遍历所有OB盘口，寻找大小球值匹配的盘口
        for (const obItem of obFullOverUnderArray) {
          // 提取大小球值（去掉"大"和"小"前缀）
          const obOverTotal = obItem.overTotal ? obItem.overTotal.replace(/^大\s*/, '').trim() : '';
          const obUnderTotal = obItem.underTotal ? obItem.underTotal.replace(/^小\s*/, '').trim() : '';
          const obOverOdds = parseFloat(obItem.overOdds) || 0;
          const obUnderOdds = parseFloat(obItem.underOdds) || 0;
          
          if (!obOverTotal || !obUnderTotal) continue;
          
          // 检查大小球值是否匹配
          if (hgOverTotal === obOverTotal && hgUnderTotal === obUnderTotal) {
            // 计算两个方向的水位
            const overUnderWater = hgOverOdds * obUnderOdds;
            const underOverWater = hgUnderOdds * obOverOdds;
            const maxWaterLevel = Math.max(overUnderWater, underOverWater);
            
            allMatches.push({
              hgItem: hgItem,
              obItem: obItem,
              hgLine: `大${hgOverTotal}[${hgOverOdds}];小${hgUnderTotal}[${hgUnderOdds}]`,
              obLine: `大${obOverTotal}[${obOverOdds}];小${obUnderTotal}[${obUnderOdds}]`,
              waterLevel: maxWaterLevel,
              overUnderWater: overUnderWater.toFixed(2),
              underOverWater: underOverWater.toFixed(2)
            });
          }
        }
      }
      
      // 按水位从高到低排序
      allMatches.sort((a, b) => b.waterLevel - a.waterLevel);
      
      return allMatches;
    }

    // 找到HG和OB平台间最佳匹配的大小球盘口 (支持多盘口匹配) - 保留兼容性
    function findBestMatchingOverUnder(hgFullOverUnderArray, obFullOverUnderArray) {
      const allMatches = findAllMatchingOverUnders(hgFullOverUnderArray, obFullOverUnderArray);
      return allMatches.length > 0 ? allMatches[0] : { hgLine: "无匹配盘口", obLine: "无匹配盘口", waterLevel: 0 };
    }

    // 保留原有函数以兼容现有代码，但内部使用新的匹配逻辑
    function findMatchingOBOverUnder(hgItem, obFullOverUnderArray) {
      if (!hgItem || !obFullOverUnderArray || !Array.isArray(obFullOverUnderArray)) return "无匹配盘口";
      
      // 提取大小球值（去掉"大"和"小"前缀）
      const hgOverTotal = hgItem.overTotal ? hgItem.overTotal.replace(/^大\s*/, '').trim() : '';
      const hgUnderTotal = hgItem.underTotal ? hgItem.underTotal.replace(/^小\s*/, '').trim() : '';
      
      // 遍历每个OB盘口，找到大小球值完全相同的那个
      for (const obItem of obFullOverUnderArray) {
        // 提取大小球值（去掉"大"和"小"前缀）
        const obOverTotal = obItem.overTotal ? obItem.overTotal.replace(/^大\s*/, '').trim() : '';
        const obUnderTotal = obItem.underTotal ? obItem.underTotal.replace(/^小\s*/, '').trim() : '';
        
        if (hgOverTotal === obOverTotal && hgUnderTotal === obUnderTotal) {
          return `大${obOverTotal}[${obItem.overOdds}];小${obUnderTotal}[${obItem.underOdds}]`;
        }
      }
      
      // 如果没找到匹配的，返回无匹配盘口
      return "无匹配盘口";
    }
    
    // 提取大小球赔率值函数 (从嵌套函数移动到全局)
    function extractOverUnderOddsValue(oddsString, isOver) {
      if (!oddsString) return 0;
      
      try {
        // 如果是对象格式（新的JSON数据结构）
        if (typeof oddsString === 'object') {
          return isOver ? parseFloat(oddsString.overOdds) || 0 : parseFloat(oddsString.underOdds) || 0;
        }
        
        // 从大小球盘口格式中提取赔率值
        // 例如 "大2.5[1.00];小2.5[0.88]"
        const parts = oddsString.split(';');
        if (parts.length !== 2) return 0;
        
        // 提取大球赔率
        const overPart = parts[0];
        const overOddsMatch = overPart.match(/\[([\d\.]+)\]/);
        const overOdds = overOddsMatch ? parseFloat(overOddsMatch[1]) : 0;
        
        // 提取小球赔率
        const underPart = parts[1];
        const underOddsMatch = underPart.match(/\[([\d\.]+)\]/);
        const underOdds = underOddsMatch ? parseFloat(underOddsMatch[1]) : 0;
        
        return isOver ? overOdds : underOdds;
      } catch (e) {
        return 0;
      }
    }
    
    // 将文本分解为字符数组，处理中文等双字节字符
    function getCharList(text) {
      if (!text || text.length === 0) return [];
      
      const result = [];
      for (let i = 0; i < text.length; i++) {
        result.push(text.charCodeAt(i));
      }
      
      return result;
    }
    
    // 计算最长公共子序列长度
    function getMaxLenSubStr(x, y) {
      const n = x.length;
      const m = y.length;
      
      // 创建DP表
      const dp = Array(n + 1).fill().map(() => Array(m + 1).fill(0));
      
      // 填充DP表
      for (let i = 0; i < n; i++) {
        for (let j = 0; j < m; j++) {
          if (x[i] === y[j]) {
            dp[i + 1][j + 1] = dp[i][j] + 1;
          } else {
            dp[i + 1][j + 1] = Math.max(dp[i][j + 1], dp[i + 1][j]);
          }
        }
      }
      
      return dp[n][m];
    }
    
    // 将小数盘口值转换为分数格式
    function convertHandicapToFraction(handicapValue) {
      if (!handicapValue || handicapValue === '?') return handicapValue;
      
      // 移除正负号并保存
      let sign = '';
      let absValue = handicapValue;
      if (handicapValue.startsWith('+') || handicapValue.startsWith('-')) {
        sign = handicapValue.charAt(0);
        absValue = handicapValue.substring(1);
      }
      
      // 转换为浮点数
      const numValue = parseFloat(absValue);
      if (isNaN(numValue)) return handicapValue;
      
      // 整数直接返回
      if (Number.isInteger(numValue)) return handicapValue;
      
      // 小数值处理 - 直接根据小数部分转换
      const decimal = numValue % 1;
      if (decimal === 0.25) {
        const intPart = Math.floor(numValue);
        return sign + intPart + "/" + (intPart + 0.5);
      } else if (decimal === 0.75) {
        const intPart = Math.floor(numValue);
        return sign + (intPart + 0.5) + "/" + (intPart + 1);
      } else {
        // 0.5和其他小数值直接返回
        return handicapValue;
      }
    }
    
    // 页面加载完成后初始化WebSocket连接
    document.addEventListener('DOMContentLoaded', function() {
      
      
      
      
      // 初始化Web Worker
      initMatchWorker();
      
      // 初始化WebSocket连接
      initWebSocket();
      
      // 初始化相似度阈值输入框
      document.getElementById('similarityThreshold').value = Math.round(similarityThreshold * 100);
      
      // 初始化水位阈值输入框
      document.getElementById('waterLevelThreshold').value = waterLevelThreshold.toFixed(2);
      
      // 初始化高亮阈值输入框
      document.getElementById('highlightThreshold').value = highlightThreshold.toFixed(2);
      
      // 初始化赔率过滤设置输入框
      document.getElementById('hgMinOdds').value = hgMinOddsFilter;
      document.getElementById('hgMaxOdds').value = hgMaxOddsFilter;
      document.getElementById('obMinOdds').value = obMinOddsFilter;
      document.getElementById('obMaxOdds').value = obMaxOddsFilter;
      
      // 初始化监控日志表格
      updateLogTable();

      // 添加用户交互监听器来初始化音频
      document.addEventListener('click', initAudio, { once: true });
      document.addEventListener('touchstart', initAudio, { once: true });

      // 启动计时器，更新所有秒数计时
      setInterval(updateAllTimers, 1000);
      
      // 启动实时同步 (使用requestAnimationFrame)
      startRealtimeSync();
      
      // 更新初始数据状态
      updateDataStatus();
      
      // 监听页面可见性变化
      let hiddenProperty = 'hidden';
      let visibilityChange = 'visibilitychange';
      
      // 检测浏览器支持的visibility属性名称
      if ('hidden' in document) {
        hiddenProperty = 'hidden';
        visibilityChange = 'visibilitychange';
      } else if ('webkitHidden' in document) {
        hiddenProperty = 'webkitHidden';
        visibilityChange = 'webkitvisibilitychange';
      } else if ('mozHidden' in document) {
        hiddenProperty = 'mozHidden';
        visibilityChange = 'mozvisibilitychange';
      } else if ('msHidden' in document) {
        hiddenProperty = 'msHidden';
        visibilityChange = 'msvisibilitychange';
      }
      
      // 监听页面可见性变化事件
      document.addEventListener(visibilityChange, function() {
        const isVisible = !document[hiddenProperty];
        console.log('页面可见性变化:', isVisible ? '可见' : '不可见');
        
        // 当页面变为不可见时，临时暂停requestAnimationFrame
        if (!isVisible) {
          try {
            // 临时暂停，但保持realtimeSyncActive状态不变
            if (animationFrameId) {
              cancelAnimationFrame(animationFrameId);
              animationFrameId = null;
              console.log('页面进入后台，暂停requestAnimationFrame');
            }
          } catch (e) {
            console.log('暂停requestAnimationFrame错误:', e);
          }
        } else {
          // 当页面恢复可见时，如果实时同步是活跃的，重新启动
          if (realtimeSyncActive && !animationFrameId) {
            try {
              animationFrameId = requestAnimationFrame(checkAndMatchData);
              console.log('页面恢复前台，重启requestAnimationFrame');
            } catch (e) {
              console.log('重启requestAnimationFrame错误:', e);
            }
          }
        }
      }, false);
      
      // 在页面卸载前关闭WebSocket连接和Worker
      window.addEventListener('beforeunload', function() {
        try {
          // 停止实时同步
          realtimeSyncActive = false;
          if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
          }
          
          // 关闭WebSocket连接
          if (socket) {
            // 移除所有事件监听器
            socket.onopen = null;
            socket.onclose = null;
            socket.onerror = null;
            socket.onmessage = null;
            socket.close();
            socket = null;
          }
          
          // 清除重连定时器
          if (wsReconnectTimer) {
            clearTimeout(wsReconnectTimer);
            wsReconnectTimer = null;
          }
          
          // 终止Worker
          if (matchWorker) {
            matchWorker.terminate();
            matchWorker = null;
          }
          
          
          
          
          console.log('页面卸载，资源已清理');
        } catch (e) {
          console.log('清理资源时出错:', e);
        }
      });
      
      // 添加页面隐藏时的处理
      window.addEventListener('pagehide', function() {
        try {
          // 停止实时同步
          if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
          }
          console.log('页面隐藏，暂停同步');
        } catch (e) {
          console.log('页面隐藏处理错误:', e);
        }
      });
    });
    
    
    // 刷新计时器缓存
    function refreshTimerCache() {
      cachedTimerElements = Array.from(document.querySelectorAll('.timer'));
      timerCacheValid = true;
    }

    // 更新所有计时器
    function updateAllTimers() {
      // 如果缓存无效，重新获取计时器元素
      if (!timerCacheValid) {
        refreshTimerCache();
      }

      // 获取当前时间（只获取一次）
      const now = Date.now();

      // 更新每个计时器
      for (let i = 0; i < cachedTimerElements.length; i++) {
        const timer = cachedTimerElements[i];

        // 检查元素是否还在DOM中
        if (!timer.isConnected) {
          timerCacheValid = false;
          continue;
        }

        // 获取预缓存的属性
        const type = timer.dataset.type;
        const id = timer.dataset.id;

        // 使用data-type和data-id属性来处理监控页面水位提示表格中的计时器
        if (type && id) {
          const storageKey = `${id}_${type}`;

          // 使用存储的时间计算持续时间（移除重复检查）
          const startTime = timerStorage[storageKey];
          if (!startTime) continue;

          // 计算持续时间（秒）
          const elapsedSeconds = Math.floor((now - startTime) / 1000);

          // 格式化为M:SS（优化字符串操作）
          const minutes = Math.floor(elapsedSeconds / 60);
          const seconds = elapsedSeconds % 60;
          const secondsStr = seconds < 10 ? `0${seconds}` : seconds.toString();

          // 更新显示
          timer.textContent = `${minutes}:${secondsStr}`;
        } else {
          // 兼容处理，使用原有的data-start属性
          const startTime = parseInt(timer.getAttribute('data-start'));
          if (!startTime) return;
          
          // 计算持续时间（秒）
          const elapsedSeconds = Math.floor((now - startTime) / 1000);
          
          // 格式化为0:SS (最多显示到9分钟)
          const minutes = Math.floor(elapsedSeconds / 60);
          const seconds = elapsedSeconds % 60;
          
          // 更新显示，分钟部分不补零
          timer.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
      }
    }
    
    // 处理被过滤掉的联赛相关的提示
    function handleFilteredLeagueAlerts(filteredMatches) {
      // 获取所有已匹配的联赛
      const matchedLeagues = new Set();
      
      // 收集过滤后的比赛中的联赛
      filteredMatches.forEach(match => {
        const league = match.hg.league || '';
        if (league) {
          matchedLeagues.add(league);
        }
      });
      
      // 检查是否有需要移除的提示
        if (activeAlerts.length > 0) {
        // 找出要移除的提示
        const alertsToRemove = activeAlerts.filter(alert => {
          return !matchedLeagues.has(alert.league);
        });
        
        // 记录被过滤掉的提示到日志
        alertsToRemove.forEach(alert => {
          addAlertToLog(alert, "联赛被过滤");
        });
        
        // 过滤掉不再匹配的联赛的提示
        activeAlerts = activeAlerts.filter(alert => {
          return matchedLeagues.has(alert.league);
        });
        
        // 如果有提示被移除，立即更新监控提醒表格和日志
        if (alertsToRemove.length > 0) {
          // 确保日志表格也被更新
          updateLogTable();
        }
      }
    }
    
    // 同步活跃提示与当前比赛数据
    function syncActiveAlertsWithCurrentMatches(filteredMatches) {
      if (activeAlerts.length === 0 || !filteredMatches || filteredMatches.length === 0) {
        return;
      }
      
      // 获取当前时间
      const now = new Date();
      
      // 为每个比赛创建一个查找映射，便于快速访问
      const matchesMap = {};
      filteredMatches.forEach(match => {
        const matchKey = `${match.hg.league || '未知'}_${match.hg.teams.home}_${match.hg.teams.away}`;
        matchesMap[matchKey] = match;
      });
      
      // 保存要移除的提示索引
      const alertsToRemove = [];
      
      // 遍历活跃提示，检查是否需要更新或移除
      for (let i = activeAlerts.length - 1; i >= 0; i--) {
        const alert = activeAlerts[i];
        const matchKey = `${alert.league}_${alert.teams.home}_${alert.teams.away}`;
        const match = matchesMap[matchKey];
        
        // 如果找到对应的比赛，更新提示的盘口和赔率
        if (match) {
          // 检查总水位是否低于水位阈值，如果是，从活跃提示中移除
          const highestOdds = Math.max(
            parseFloat(alert.fullTimeHandicap.totalOdds) || 0,
            parseFloat(alert.fullTimeOverUnder.totalOdds) || 0,
            parseFloat(alert.halfTimeHandicap.totalOdds) || 0,
            parseFloat(alert.halfTimeOverUnder.totalOdds) || 0
          );
          
if (highestOdds < waterLevelThreshold) {
            // 标记此提示为需要移除
            alertsToRemove.push({index: i, reason: "水位降低", alert: alert});
          }
        } else {
          // 如果找不到对应的比赛，也标记为需要移除
          alertsToRemove.push({index: i, reason: "比赛不再匹配", alert: alert});
        }
      }
      
      // 从后往前移除提示，避免索引变化问题
      for (let i = 0; i < alertsToRemove.length; i++) {
        const {index, reason, alert} = alertsToRemove[i];
        
        // 记录日志
        addAlertToLog(alert, reason);
        
        // 不要删除timerStorage中的条目，让它们保持原样
        activeAlerts.splice(index, 1);
      }
      
      // 如果有提示被移除，立即更新日志表格
      if (alertsToRemove.length > 0) {
        updateLogTable();
      }
    }
    
    // 存储监控日志记录
    let logRecords = [];
    
    // 添加提示到监控日志
    function addAlertToLog(alert, reason) {
      try {
        // 获取当前时间
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        
        // 计算提示持续时间
        let duration = "未知";
        
        // 检查每种盘口类型
        const checkOddsType = (oddsType, typeKey) => {
          if (!alert[oddsType] || !alert[oddsType].totalOdds) return null;
          
          const matchId = `${alert.league}_${alert.teams.home}_${alert.teams.away}`;
          const storageKey = `${matchId}_${typeKey}`;
          
          if (timerStorage[storageKey]) {
            const startTime = timerStorage[storageKey];
            const elapsedMs = now.getTime() - startTime;
            const elapsedSeconds = Math.floor(elapsedMs / 1000);
            const minutes = Math.floor(elapsedSeconds / 60);
            const seconds = elapsedSeconds % 60;
            
            // 转换盘口类型为中文
            let typeChinese;
            switch(oddsType) {
              case 'fullTimeHandicap':
                typeChinese = '全场让球';
                break;
              case 'fullTimeOverUnder':
                typeChinese = '全场大小';
                break;
              case 'halfTimeHandicap':
                typeChinese = '半场让球';
                break;
              case 'halfTimeOverUnder':
                typeChinese = '半场大小';
                break;
              default:
                typeChinese = oddsType.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            }
            
            return {
              type: typeChinese,
              data: `${alert[oddsType].hgData}\n${alert[oddsType].obData}`,
              odds: alert[oddsType].totalOdds,
              duration: `${minutes}:${seconds.toString().padStart(2, '0')}`
            };
          }
          return null;
        };
        
        // 检查所有盘口类型
        const types = [
          { name: 'fullTimeHandicap', key: 'fth' },
          { name: 'fullTimeOverUnder', key: 'ftou' },
          { name: 'halfTimeHandicap', key: 'hth' },
          { name: 'halfTimeOverUnder', key: 'htou' }
        ];
        
        // 收集所有有效的盘口记录
        const oddsRecords = [];
        types.forEach(type => {
          const record = checkOddsType(type.name, type.key);
          if (record) oddsRecords.push(record);
        });
        
        // 为每个有效的盘口创建一条日志记录
        oddsRecords.forEach(record => {
          // 创建日志记录
          const logRecord = {
            time: timeString,
            league: alert.league,
            homeTeam: alert.teams.home,
            awayTeam: alert.teams.away,
            oddsType: record.type,
            data: record.data,
            odds: record.odds,
            duration: record.duration,
            reason: reason
          };
          
          // 添加到日志记录数组
          logRecords.push(logRecord);
          
          // 每次添加记录后立即更新日志表格
          console.log(`添加日志记录: ${record.type} - ${record.odds} - ${reason}`);
        });
        
        // 如果没有有效的盘口记录，创建一条通用记录
        if (oddsRecords.length === 0) {
          const logRecord = {
            time: timeString,
            league: alert.league,
            homeTeam: alert.teams.home,
            awayTeam: alert.teams.away,
            oddsType: "未知",
            data: "无数据",
            odds: "0.00",
            duration: "00:00",
            reason: reason
          };
          
          // 添加到日志记录数组
          logRecords.push(logRecord);
          console.log(`添加通用日志记录: ${reason}`);
        }
        
        // 限制日志记录数量，保留最新的100条
        if (logRecords.length > 100) {
          logRecords = logRecords.slice(-100);
        }
        
      } catch (e) {
        console.error("添加监控日志出错:", e);
      }
    }
    
    // 更新监控提醒JSON数据
    function updateAlertJsonData(alerts) {
      try {
        const jsonDataTextarea = document.getElementById('alertJsonData');
        if (!jsonDataTextarea) return;
        
        if (!alerts || alerts.length === 0) {
          jsonDataTextarea.value = JSON.stringify({ alerts: [] }, null, 2);
          // 同时清空所有betStr输入框
          const fthBetStrTextarea = document.getElementById('fthBetStrData');
          const ftouBetStrTextarea = document.getElementById('ftouBetStrData');
          const hthBetStrTextarea = document.getElementById('hthBetStrData');
          const htouBetStrTextarea = document.getElementById('htouBetStrData');
          if (fthBetStrTextarea) fthBetStrTextarea.value = '';
          if (ftouBetStrTextarea) ftouBetStrTextarea.value = '';
          if (hthBetStrTextarea) hthBetStrTextarea.value = '';
          if (htouBetStrTextarea) htouBetStrTextarea.value = '';
          return;
        }
        
        // 只显示监控提醒表格中的数据，不包含副盘口信息
        const simplifiedAlerts = alerts.map(alert => {
          // 处理全场让球盘口数据
          const fullTimeHandicap = {
            totalOdds: alert.fullTimeHandicap.totalOdds
          };
          
          if (alert.fullTimeHandicap.hgData) {
            const hgParts = alert.fullTimeHandicap.hgData.split('|');

            fullTimeHandicap.hg = {
              direction: hgParts[0] || '',
              handicap: hgParts[1] || '',
              odds: hgParts[2] || '',
              betStr: hgParts[3] || '' // 从HG数据中直接提取投注字符串
            };
          }
          
          if (alert.fullTimeHandicap.obData) {
            const obParts = alert.fullTimeHandicap.obData.split('|');
            fullTimeHandicap.ob = {
              handicap: obParts[0] || '',
              homeOdds: obParts[1] || '',
              awayOdds: obParts[2] || ''
            };
          }
          
          // 处理全场大小球盘口数据
          const fullTimeOverUnder = {
            totalOdds: alert.fullTimeOverUnder.totalOdds
          };
          
          if (alert.fullTimeOverUnder.hgData) {
            const hgParts = alert.fullTimeOverUnder.hgData.split('|');

            fullTimeOverUnder.hg = {
              direction: hgParts[0] || '',
              total: hgParts[1] || '',
              odds: hgParts[2] || '',
              betStr: hgParts[3] || '' // 从HG数据中直接提取投注字符串
            };
          }
          
          if (alert.fullTimeOverUnder.obData) {
            const obParts = alert.fullTimeOverUnder.obData.split('|');
            fullTimeOverUnder.ob = {
              total: obParts[0] || '',
              overOdds: obParts[1] || '',
              underOdds: obParts[2] || ''
            };
          }
          
          // 处理半场让球盘口数据
          const halfTimeHandicap = {
            totalOdds: alert.halfTimeHandicap.totalOdds
          };
          
          if (alert.halfTimeHandicap.hgData) {
            const hgParts = alert.halfTimeHandicap.hgData.split('|');

            halfTimeHandicap.hg = {
              direction: hgParts[0] || '',
              handicap: hgParts[1] || '',
              odds: hgParts[2] || '',
              betStr: hgParts[3] || '' // 从HG数据中直接提取投注字符串
            };
          }
          
          if (alert.halfTimeHandicap.obData) {
            const obParts = alert.halfTimeHandicap.obData.split('|');
            halfTimeHandicap.ob = {
              handicap: obParts[0] || '',
              homeOdds: obParts[1] || '',
              awayOdds: obParts[2] || ''
            };
          }
          
          // 处理半场大小球盘口数据
          const halfTimeOverUnder = {
            totalOdds: alert.halfTimeOverUnder.totalOdds
          };
          
          if (alert.halfTimeOverUnder.hgData) {
            const hgParts = alert.halfTimeOverUnder.hgData.split('|');

            halfTimeOverUnder.hg = {
              direction: hgParts[0] || '',
              total: hgParts[1] || '',
              odds: hgParts[2] || '',
              betStr: hgParts[3] || '' // 从HG数据中直接提取投注字符串
            };
          }
          
          if (alert.halfTimeOverUnder.obData) {
            const obParts = alert.halfTimeOverUnder.obData.split('|');
            halfTimeOverUnder.ob = {
              total: obParts[0] || '',
              overOdds: obParts[1] || '',
              underOdds: obParts[2] || ''
            };
          }
          
          // 返回简化的监控提醒数据，只包含表格中显示的内容
          return {
            league: alert.league,
            matchTime: alert.matchTime,
            teams: alert.teams,
            hgTeams: alert.hgTeams,
            obTeams: alert.obTeams,
            alertTime: alert.alertTime,
            fullTimeHandicap: fullTimeHandicap,
            fullTimeOverUnder: fullTimeOverUnder,
            halfTimeHandicap: halfTimeHandicap,
            halfTimeOverUnder: halfTimeOverUnder
          };
        });
        
        // 检查是否已有JSON数据，如果有则合并而不是覆盖
        const newJsonData = { alerts: simplifiedAlerts };
        const existingJsonData = jsonDataTextarea.value.trim() ?
          (() => {
            try {
              return JSON.parse(jsonDataTextarea.value);
            } catch (e) {
              return null;
            }
          })() : null;

        let finalJsonData;
        if (existingJsonData && existingJsonData.alerts) {
          // 合并数据，保留投注字符串更新的赔率
          finalJsonData = mergeJsonDataPreserveBetStringOdds(newJsonData, existingJsonData);
        } else {
          finalJsonData = newJsonData;
        }

        // 将合并后的数据转换为JSON字符串并显示在输入框中
        jsonDataTextarea.value = JSON.stringify(finalJsonData, null, 2);

        // 提取所有betStr值并显示在betStr输入框中
        updateBetStrData(simplifiedAlerts);
      } catch (e) {
        console.error("更新监控提醒JSON数据出错:", e);
      }
    }

    // 更新betStr数据显示
    function updateBetStrData(alerts) {
      try {
        // 获取所有输入框元素
        const fthBetStrTextarea = document.getElementById('fthBetStrData');
        const ftouBetStrTextarea = document.getElementById('ftouBetStrData');
        const hthBetStrTextarea = document.getElementById('hthBetStrData');
        const htouBetStrTextarea = document.getElementById('htouBetStrData');

        // 动态获取UID，优先从hgData获取，如果没有则使用默认值
        let uid = '63tj0r1gm37976245l6599788b1'; // 默认UID
        if (hgData && hgData.UID) {
          uid = hgData.UID;
        }

        if (!alerts || alerts.length === 0) {
          // 清空所有输入框
          if (fthBetStrTextarea) fthBetStrTextarea.value = '';
          if (ftouBetStrTextarea) ftouBetStrTextarea.value = '';
          if (hthBetStrTextarea) hthBetStrTextarea.value = '';
          if (htouBetStrTextarea) htouBetStrTextarea.value = '';
          return;
        }

        // 分别收集不同盘口类型的betStr
        const fthBetStrs = [];  // 全场让球
        const ftouBetStrs = []; // 全场大小球
        const hthBetStrs = [];  // 半场让球
        const htouBetStrs = []; // 半场大小球

        // 遍历所有监控提醒，按类型提取betStr值
        alerts.forEach(alert => {
          // 全场让球
          if (alert.fullTimeHandicap && alert.fullTimeHandicap.hg && alert.fullTimeHandicap.hg.betStr) {
            fthBetStrs.push(alert.fullTimeHandicap.hg.betStr);
          }

          // 全场大小球
          if (alert.fullTimeOverUnder && alert.fullTimeOverUnder.hg && alert.fullTimeOverUnder.hg.betStr) {
            ftouBetStrs.push(alert.fullTimeOverUnder.hg.betStr);
          }

          // 半场让球
          if (alert.halfTimeHandicap && alert.halfTimeHandicap.hg && alert.halfTimeHandicap.hg.betStr) {
            hthBetStrs.push(alert.halfTimeHandicap.hg.betStr);
          }

          // 半场大小球
          if (alert.halfTimeOverUnder && alert.halfTimeOverUnder.hg && alert.halfTimeOverUnder.hg.betStr) {
            htouBetStrs.push(alert.halfTimeOverUnder.hg.betStr);
          }
        });

        // 格式化为完整URL并更新对应的输入框
        if (fthBetStrTextarea) {
          const uniqueFthBetStrs = [...new Set(fthBetStrs)];
          if (uniqueFthBetStrs.length > 0 && uniqueFthBetStrs[0]) {
            const betStrValue = uniqueFthBetStrs.join('^');
            fthBetStrTextarea.value = `p=Total_order_view&uid=${uid}&ver=2025-07-24-no319_108&langx=zh-cn&odd_f_type=H&betStr=${betStrValue}&code=getOrderview&needsP3=Y`;
          } else {
            fthBetStrTextarea.value = '';
          }
        }

        if (ftouBetStrTextarea) {
          const uniqueFtouBetStrs = [...new Set(ftouBetStrs)];
          if (uniqueFtouBetStrs.length > 0 && uniqueFtouBetStrs[0]) {
            const betStrValue = uniqueFtouBetStrs.join('^');
            ftouBetStrTextarea.value = `p=Total_order_view&uid=${uid}&ver=2025-07-24-no319_108&langx=zh-cn&odd_f_type=H&betStr=${betStrValue}&code=getOrderview&needsP3=Y`;
          } else {
            ftouBetStrTextarea.value = '';
          }
        }

        if (hthBetStrTextarea) {
          const uniqueHthBetStrs = [...new Set(hthBetStrs)];
          if (uniqueHthBetStrs.length > 0 && uniqueHthBetStrs[0]) {
            const betStrValue = uniqueHthBetStrs.join('^');
            hthBetStrTextarea.value = `p=Total_order_view&uid=${uid}&ver=2025-07-24-no319_108&langx=zh-cn&odd_f_type=H&betStr=${betStrValue}&code=getOrderview&needsP3=Y`;
          } else {
            hthBetStrTextarea.value = '';
          }
        }

        if (htouBetStrTextarea) {
          const uniqueHtouBetStrs = [...new Set(htouBetStrs)];
          if (uniqueHtouBetStrs.length > 0 && uniqueHtouBetStrs[0]) {
            const betStrValue = uniqueHtouBetStrs.join('^');
            htouBetStrTextarea.value = `p=Total_order_view&uid=${uid}&ver=2025-07-24-no319_108&langx=zh-cn&odd_f_type=H&betStr=${betStrValue}&code=getOrderview&needsP3=Y`;
          } else {
            htouBetStrTextarea.value = '';
          }
        }

      } catch (e) {
        console.error("更新betStr数据出错:", e);
      }
    }
    
    // 更新监控日志表格
    function updateLogTable() {
      try {
        const logTableBody = document.getElementById('logTableBody');
        if (!logTableBody) return;
        
        // 清空表格
        logTableBody.innerHTML = '';
        
        // 如果没有日志记录
        if (logRecords.length === 0) {
          const row = document.createElement('tr');
          row.innerHTML = '<td colspan="9" style="text-align: center; padding: 8px; font-size: 13px; font-weight: normal; color: #666;">暂无监控日志</td>';
          logTableBody.appendChild(row);
          return;
        }
        
        // 按时间倒序排列日志记录（最新的在前面）
        const sortedRecords = [...logRecords].reverse();
        
        // 添加每一行
        sortedRecords.forEach(record => {
          const row = document.createElement('tr');
          
          // 设置行内容
          row.innerHTML = `
            <td style="padding: 4px; text-align: center; font-size: 12px; font-weight: normal;">${record.time}</td>
            <td style="padding: 4px; text-align: left; font-size: 12px; font-weight: normal;">${record.league}</td>
            <td style="padding: 4px; text-align: left; font-size: 12px; font-weight: normal;">${record.homeTeam}</td>
            <td style="padding: 4px; text-align: left; font-size: 12px; font-weight: normal;">${record.awayTeam}</td>
            <td style="padding: 4px; text-align: center; font-size: 12px; font-weight: normal;">${record.oddsType}</td>
            <td style="padding: 4px; text-align: left; font-size: 12px; font-weight: normal; white-space: pre-line;">${record.data}</td>
            <td style="padding: 4px; text-align: center; font-size: 12px; font-weight: normal; color: ${parseFloat(record.odds) > highlightThreshold ? '#ff0000' : '#000'};">${record.odds}</td>
            <td style="padding: 4px; text-align: center; font-size: 12px; font-weight: normal;">${record.duration}</td>
            <td style="padding: 4px; text-align: center; font-size: 12px; font-weight: normal;">${record.reason}</td>
          `;
          
          // 添加到表格
          logTableBody.appendChild(row);
        });
      } catch (e) {
        console.error("更新监控日志表格出错:", e);
      }
    }
  </script>
  
  <!-- 添加主标签切换函数 -->
  <script>
    // 切换主标签页
    function switchMainTab(tabId) {
      // 隐藏所有主内容区域
      document.querySelectorAll('.main-content').forEach(content => {
        content.classList.remove('active');
      });
      
      // 取消所有标签页的激活状态
      document.querySelectorAll('.main-tab').forEach(tab => {
        tab.classList.remove('active');
      });
      
      // 激活所选标签页
      document.getElementById(`main-tab-${tabId}`).classList.add('active');
      
      // 显示对应的内容区域
      document.getElementById(`main-content-${tabId}`).classList.add('active');
    }

    // 自动提交投注字符串数据的功能
    let autoSubmitInterval = null;
    let currentSubmitIndex = 0; // 当前提交的索引：0=全场让球, 1=全场大小, 2=半场让球, 3=半场大小

    // 存储投注字符串返回的赔率数据
    let betStringOddsData = {
      fullTimeHandicap: {},     // 全场让球: { gameId: odds }
      fullTimeOverUnder: {},    // 全场大小: { gameId: odds }
      halfTimeHandicap: {},     // 半场让球: { gameId: odds }
      halfTimeOverUnder: {}     // 半场大小: { gameId: odds }
    };

    // JSON数据检查间隔
    let jsonCheckInterval = null;

    // 解析XML响应并提取赔率和ID
    function parseOddsFromXML(xmlString) {
      try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlString, 'text/xml');

        // 提取赔率
        const ioratioElement = xmlDoc.querySelector('ioratio');
        const odds = ioratioElement ? parseFloat(ioratioElement.textContent) : null;

        // 提取betslip ID
        const betslipElement = xmlDoc.querySelector('betslip');
        const betslipId = betslipElement ? betslipElement.getAttribute('id') : null;

        // 从 id='FT_9761027' 中提取数字部分 9761027
        let gameId = null;
        if (betslipId) {
          const match = betslipId.match(/FT_(\d+)/);
          gameId = match ? match[1] : null;
        }

        return {
          odds: isNaN(odds) ? null : odds,
          gameId: gameId
        };
      } catch (error) {
        return { odds: null, gameId: null };
      }
    }

    // 精确匹配投注字符串类型
    function isExactBetStrMatch(betStr, gameId, betType) {
      if (!betStr || !gameId) return false;

      // 首先检查是否以gameId开头
      if (!betStr.startsWith(gameId + '!')) return false;

      // 根据投注类型检查特定的标识符
      switch (betType) {
        case 0: // 全场让球
          return betStr.includes('!RE!REH!') || betStr.includes('!RE!REC!');
        case 1: // 全场大小球
          return betStr.includes('!ROU!ROUC!') || betStr.includes('!ROU!ROUH!');
        case 2: // 半场让球
          return betStr.includes('!HRE!HREH!') || betStr.includes('!HRE!HREC!');
        case 3: // 半场大小球
          return betStr.includes('!HROU!HROUC!') || betStr.includes('!HROU!HROUH!');
        default:
          return false;
      }
    }

    // 存储投注字符串返回的赔率数据
    function storeBetStringOdds(betType, newOdds, gameId) {
      console.log(`[storeBetStringOdds] 存储赔率数据: betType=${betType}, newOdds=${newOdds}, gameId=${gameId}`);

      if (!newOdds || !gameId) {
        console.log(`[storeBetStringOdds] 参数无效: newOdds=${newOdds}, gameId=${gameId}`);
        return;
      }

      const odds = parseFloat(newOdds);

      // 根据投注类型存储到对应的变量中
      switch (betType) {
        case 0: // 全场让球
          betStringOddsData.fullTimeHandicap[gameId] = odds;
          console.log(`[storeBetStringOdds] 存储全场让球赔率: gameId=${gameId}, odds=${odds}`);
          break;
        case 1: // 全场大小
          betStringOddsData.fullTimeOverUnder[gameId] = odds;
          console.log(`[storeBetStringOdds] 存储全场大小赔率: gameId=${gameId}, odds=${odds}`);
          break;
        case 2: // 半场让球
          betStringOddsData.halfTimeHandicap[gameId] = odds;
          console.log(`[storeBetStringOdds] 存储半场让球赔率: gameId=${gameId}, odds=${odds}`);
          break;
        case 3: // 半场大小
          betStringOddsData.halfTimeOverUnder[gameId] = odds;
          console.log(`[storeBetStringOdds] 存储半场大小赔率: gameId=${gameId}, odds=${odds}`);
          break;
      }
    }



    // 从JSON数据同步更新监控表格
    function updateMonitorTableFromJSON(jsonData) {
      if (!jsonData || !jsonData.alerts || !Array.isArray(jsonData.alerts)) return;

      jsonData.alerts.forEach(alert => {
        const matchKey = `${alert.matchInfo?.league || ''}_${alert.matchInfo?.teams?.home || ''}_${alert.matchInfo?.teams?.away || ''}`;

        // 更新全场让球
        if (alert.fullTimeHandicap) {
          updateTableCell(matchKey, 'fullTimeHandicap', alert.fullTimeHandicap);
        }

        // 更新全场大小球
        if (alert.fullTimeOverUnder) {
          updateTableCell(matchKey, 'fullTimeOverUnder', alert.fullTimeOverUnder);
        }

        // 更新半场让球
        if (alert.halfTimeHandicap) {
          updateTableCell(matchKey, 'halfTimeHandicap', alert.halfTimeHandicap);
        }

        // 更新半场大小球
        if (alert.halfTimeOverUnder) {
          updateTableCell(matchKey, 'halfTimeOverUnder', alert.halfTimeOverUnder);
        }
      });
    }

    // 更新表格中的具体单元格
    function updateTableCell(matchKey, betType, alertData) {
      // 构建单元格ID
      const cellId = `${matchKey}_${betType}`;
      const cell = document.getElementById(cellId);

      if (!cell) return;

      // 优先使用投注字符串返回的HG赔率
      const hgOdds = parseFloat(alertData.hg?.odds || 0);
      const obOdds = parseFloat(alertData.ob?.odds || 0);
      const totalOdds = parseFloat(alertData.totalOdds || 0);

      // 构建显示内容
      let displayContent = '';

      if (betType.includes('Handicap')) {
        // 让球盘口
        const hgDirection = alertData.hg?.direction || '';
        const hgHandicap = alertData.hg?.handicap || '';
        const obDirection = alertData.ob?.direction || '';
        const obHandicap = alertData.ob?.handicap || '';

        displayContent = `
          <div class="odds-display">
            <div class="hg-odds">HG: ${hgDirection} ${hgHandicap} @ ${hgOdds.toFixed(2)}</div>
            <div class="ob-odds">OB: ${obDirection} ${obHandicap} @ ${obOdds.toFixed(2)}</div>
            <div class="total-odds">总水位: ${totalOdds.toFixed(2)}</div>
          </div>
        `;
      } else {
        // 大小球盘口
        const hgDirection = alertData.hg?.direction || '';
        const hgTotal = alertData.hg?.total || '';
        const obDirection = alertData.ob?.direction || '';
        const obTotal = alertData.ob?.total || '';

        displayContent = `
          <div class="odds-display">
            <div class="hg-odds">HG: ${hgDirection} ${hgTotal} @ ${hgOdds.toFixed(2)}</div>
            <div class="ob-odds">OB: ${obDirection} ${obTotal} @ ${obOdds.toFixed(2)}</div>
            <div class="total-odds">总水位: ${totalOdds.toFixed(2)}</div>
          </div>
        `;
      }

      // 更新单元格内容
      cell.innerHTML = displayContent;

      // 添加更新高亮效果
      cell.classList.add('updated');
      setTimeout(() => {
        cell.classList.remove('updated');
      }, 2000);
    }







    // 合并JSON数据，保留投注字符串更新的赔率
    function mergeJsonDataPreserveBetStringOdds(newJsonData, existingJsonData) {
      try {
        // 创建现有数据的索引
        const existingAlertsMap = new Map();
        existingJsonData.alerts.forEach(alert => {
          const matchKey = `${alert.matchInfo?.league || ''}_${alert.matchInfo?.teams?.home || ''}_${alert.matchInfo?.teams?.away || ''}`;
          existingAlertsMap.set(matchKey, alert);
        });

        // 合并新数据和现有数据
        const mergedAlerts = newJsonData.alerts.map(newAlert => {
          const matchKey = `${newAlert.matchInfo?.league || ''}_${newAlert.matchInfo?.teams?.home || ''}_${newAlert.matchInfo?.teams?.away || ''}`;
          const existingAlert = existingAlertsMap.get(matchKey);

          if (!existingAlert) {
            return newAlert; // 新比赛，直接使用新数据
          }

          // 合并每个盘口类型的数据，保留投注字符串赔率
          const mergedAlert = { ...newAlert };

          // 合并全场让球数据
          if (newAlert.fullTimeHandicap && existingAlert.fullTimeHandicap) {
            mergedAlert.fullTimeHandicap = mergeMarketDataPreserveBetString(
              newAlert.fullTimeHandicap,
              existingAlert.fullTimeHandicap
            );
          }

          // 合并全场大小球数据
          if (newAlert.fullTimeOverUnder && existingAlert.fullTimeOverUnder) {
            mergedAlert.fullTimeOverUnder = mergeMarketDataPreserveBetString(
              newAlert.fullTimeOverUnder,
              existingAlert.fullTimeOverUnder
            );
          }

          // 合并半场让球数据
          if (newAlert.halfTimeHandicap && existingAlert.halfTimeHandicap) {
            mergedAlert.halfTimeHandicap = mergeMarketDataPreserveBetString(
              newAlert.halfTimeHandicap,
              existingAlert.halfTimeHandicap
            );
          }

          // 合并半场大小球数据
          if (newAlert.halfTimeOverUnder && existingAlert.halfTimeOverUnder) {
            mergedAlert.halfTimeOverUnder = mergeMarketDataPreserveBetString(
              newAlert.halfTimeOverUnder,
              existingAlert.halfTimeOverUnder
            );
          }

          return mergedAlert;
        });

        return { alerts: mergedAlerts };

      } catch (error) {
        console.error('合并JSON数据时出错:', error);
        return newJsonData; // 出错时返回新数据
      }
    }

    // 合并单个盘口数据，保留投注字符串赔率
    function mergeMarketDataPreserveBetString(newMarketData, existingMarketData) {
      const merged = { ...newMarketData };

      // 如果现有数据的HG赔率是通过投注字符串更新的，则保留它
      if (existingMarketData.hg &&
          existingMarketData.hg.odds &&
          existingMarketData.hg.betStr === newMarketData.hg?.betStr) {

        // 检查现有赔率是否与新的外部赔率不同（说明是投注字符串更新的）
        const existingOdds = parseFloat(existingMarketData.hg.odds);
        const newExternalOdds = parseFloat(newMarketData.hg?.odds || 0);

        if (existingOdds !== newExternalOdds && existingOdds > 0) {
          // 保留投注字符串更新的HG赔率
          merged.hg = { ...merged.hg, odds: existingMarketData.hg.odds };

          // 重新计算总水位（使用投注字符串的HG赔率 × 新的OB赔率）
          const hgOdds = existingOdds;
          const obOdds = parseFloat(newMarketData.ob?.odds || 1);
          merged.totalOdds = (hgOdds * obOdds).toFixed(2);
        }
      }

      return merged;
    }

    // 每500毫秒检查JSON数据并匹配投注字符串变量中的赔率
    function checkAndUpdateJsonFromBetStringData() {
      try {
        const jsonDataTextarea = document.getElementById('alertJsonData');
        if (!jsonDataTextarea || !jsonDataTextarea.value.trim()) return;

        const jsonData = JSON.parse(jsonDataTextarea.value);
        if (!jsonData.alerts || !Array.isArray(jsonData.alerts)) return;

        let hasUpdates = false;

        // 遍历JSON数据中的每个监控提醒
        jsonData.alerts.forEach((alert, alertIndex) => {
          // 检查全场让球
          if (alert.fullTimeHandicap?.hg?.betStr) {
            const gameIdMatch = alert.fullTimeHandicap.hg.betStr.match(/^(\d+)!/);
            if (gameIdMatch) {
              const gameId = gameIdMatch[1];
              const storedOdds = betStringOddsData.fullTimeHandicap[gameId];
              const currentOdds = parseFloat(alert.fullTimeHandicap.hg.odds);

              if (storedOdds && Math.abs(storedOdds - currentOdds) > 0.001) {
                console.log(`[checkAndUpdateJsonFromBetStringData] 更新全场让球: gameId=${gameId}, ${currentOdds} → ${storedOdds}`);
                alert.fullTimeHandicap.hg.odds = storedOdds.toFixed(2);
                const obOdds = parseFloat(alert.fullTimeHandicap.ob?.odds || 1);
                alert.fullTimeHandicap.totalOdds = (storedOdds * obOdds).toFixed(2);
                hasUpdates = true;
              }
            }
          }

          // 检查全场大小球
          if (alert.fullTimeOverUnder?.hg?.betStr) {
            const gameIdMatch = alert.fullTimeOverUnder.hg.betStr.match(/^(\d+)!/);
            if (gameIdMatch) {
              const gameId = gameIdMatch[1];
              const storedOdds = betStringOddsData.fullTimeOverUnder[gameId];
              const currentOdds = parseFloat(alert.fullTimeOverUnder.hg.odds);

              if (storedOdds && Math.abs(storedOdds - currentOdds) > 0.001) {
                console.log(`[checkAndUpdateJsonFromBetStringData] 更新全场大小: gameId=${gameId}, ${currentOdds} → ${storedOdds}`);
                alert.fullTimeOverUnder.hg.odds = storedOdds.toFixed(2);
                const obOdds = parseFloat(alert.fullTimeOverUnder.ob?.odds || 1);
                alert.fullTimeOverUnder.totalOdds = (storedOdds * obOdds).toFixed(2);
                hasUpdates = true;
              }
            }
          }

          // 检查半场让球
          if (alert.halfTimeHandicap?.hg?.betStr) {
            const gameIdMatch = alert.halfTimeHandicap.hg.betStr.match(/^(\d+)!/);
            if (gameIdMatch) {
              const gameId = gameIdMatch[1];
              const storedOdds = betStringOddsData.halfTimeHandicap[gameId];
              const currentOdds = parseFloat(alert.halfTimeHandicap.hg.odds);

              if (storedOdds && Math.abs(storedOdds - currentOdds) > 0.001) {
                console.log(`[checkAndUpdateJsonFromBetStringData] 更新半场让球: gameId=${gameId}, ${currentOdds} → ${storedOdds}`);
                alert.halfTimeHandicap.hg.odds = storedOdds.toFixed(2);
                const obOdds = parseFloat(alert.halfTimeHandicap.ob?.odds || 1);
                alert.halfTimeHandicap.totalOdds = (storedOdds * obOdds).toFixed(2);
                hasUpdates = true;
              }
            }
          }

          // 检查半场大小球
          if (alert.halfTimeOverUnder?.hg?.betStr) {
            const gameIdMatch = alert.halfTimeOverUnder.hg.betStr.match(/^(\d+)!/);
            if (gameIdMatch) {
              const gameId = gameIdMatch[1];
              const storedOdds = betStringOddsData.halfTimeOverUnder[gameId];
              const currentOdds = parseFloat(alert.halfTimeOverUnder.hg.odds);

              if (storedOdds && Math.abs(storedOdds - currentOdds) > 0.001) {
                console.log(`[checkAndUpdateJsonFromBetStringData] 更新半场大小: gameId=${gameId}, ${currentOdds} → ${storedOdds}`);
                alert.halfTimeOverUnder.hg.odds = storedOdds.toFixed(2);
                const obOdds = parseFloat(alert.halfTimeOverUnder.ob?.odds || 1);
                alert.halfTimeOverUnder.totalOdds = (storedOdds * obOdds).toFixed(2);
                hasUpdates = true;
              }
            }
          }
        });

        // 如果有更新，同步到JSON显示区域和监控表格
        if (hasUpdates) {
          jsonDataTextarea.value = JSON.stringify(jsonData, null, 2);

          // 如果有JSON树形显示，也更新它
          if (typeof jsonTreeViewer !== 'undefined' && jsonTreeViewer) {
            jsonTreeViewer.render(jsonData);
          }

          // 同步更新监控表格显示
          updateMonitorTableFromJSON(jsonData);
          console.log(`[checkAndUpdateJsonFromBetStringData] JSON数据和表格已同步更新`);
        }

      } catch (error) {
        console.error('[checkAndUpdateJsonFromBetStringData] 错误:', error);
      }
    }

    function submitCurrentBetString() {
      try {
        const jsonDataTextarea = document.getElementById('alertJsonData');
        if (!jsonDataTextarea || !jsonDataTextarea.value.trim()) return;

        const jsonData = JSON.parse(jsonDataTextarea.value);
        if (!jsonData.alerts || !Array.isArray(jsonData.alerts)) return;

        // 根据当前索引确定要检查的盘口类型
        const betTypeNames = ['fullTimeHandicap', 'fullTimeOverUnder', 'halfTimeHandicap', 'halfTimeOverUnder'];
        const currentBetTypeName = betTypeNames[currentSubmitIndex];

        // 查找当前盘口类型的投注字符串
        let foundBetStr = null;
        let currentOdds = null;

        for (const alert of jsonData.alerts) {
          const marketData = alert[currentBetTypeName];
          if (marketData?.hg?.betStr) {
            foundBetStr = marketData.hg.betStr;
            currentOdds = marketData.hg.odds;
            break; // 找到第一个就使用
          }
        }

        if (foundBetStr) {
          // 从投注字符串中提取gameId
          const gameIdMatch = foundBetStr.match(/^(\d+)!/);
          if (gameIdMatch) {
            const gameId = gameIdMatch[1];
            const uid = '63tj0r1gm37976245l6599788b1';
            const postData = `p=Total_order_view&uid=${uid}&ver=2025-07-24-no319_108&langx=zh-cn&odd_f_type=H&betStr=${foundBetStr}&code=getOrderview&needsP3=Y`;

            // 发送请求获取最新赔率
            fetch('/custom-game-data', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
              },
              body: JSON.stringify({
                postData: postData,
                apiUrl: 'https://m061.mos077.com/transform.php?ver=2025-05-15-C1bug_92'
              })
            }).then(response => {
              if (response.ok) {
                return response.text();
              }
              throw new Error('Response not ok');
            }).then(xmlData => {
              const result = parseOddsFromXML(xmlData);
              if (result.odds && result.gameId) {
                // 存储投注字符串返回的赔率数据
                console.log(`[submitCurrentBetString] 存储赔率数据: betType=${currentSubmitIndex}, 赔率=${result.odds}`);
                storeBetStringOdds(currentSubmitIndex, result.odds, result.gameId);
              }
            }).catch(() => {
              // 忽略错误
            });
          }
        } else {
          console.log(`[submitCurrentBetString] 未找到 ${currentBetTypeName} 的投注字符串`);
        }

      } catch (error) {
        console.error('[submitCurrentBetString] 错误:', error);
      }

      // 移动到下一个投注字符串（循环）
      currentSubmitIndex = (currentSubmitIndex + 1) % 4;
    }

    function toggleAutoSubmit() {
      const submitBtn = document.getElementById('submitJsonBtn');

      if (autoSubmitInterval) {
        // 停止自动提交
        clearInterval(autoSubmitInterval);
        clearInterval(jsonCheckInterval);            // 清除JSON检查定时器
        autoSubmitInterval = null;
        jsonCheckInterval = null;                    // 重置变量
        currentSubmitIndex = 0; // 重置索引
        submitBtn.textContent = '📤 开始自动提交';
        submitBtn.style.backgroundColor = '#007bff';
      } else {
        // 开始自动提交
        currentSubmitIndex = 0; // 从全场让球开始
        autoSubmitInterval = setInterval(submitCurrentBetString, 1200);
        jsonCheckInterval = setInterval(checkAndUpdateJsonFromBetStringData, 500); // 每500毫秒检查JSON数据
        submitBtn.textContent = '⏹️ 停止自动提交';
        submitBtn.style.backgroundColor = '#dc3545';
      }
    }

    // 绑定提交按钮事件
    document.addEventListener('DOMContentLoaded', function() {
      const submitJsonBtn = document.getElementById('submitJsonBtn');
      if (submitJsonBtn) {
        submitJsonBtn.addEventListener('click', toggleAutoSubmit);
      }
    });
  </script>
  
  <!-- 主标签页导航 (底部) -->
  <div class="main-tabs" style="position: fixed; bottom: 0; left: 0; right: 0; border-top: 1px solid #ddd; border-bottom: none; background-color: #fff; z-index: 1000; border-radius: 0;">
    <div id="main-tab-matches" class="main-tab active" onclick="switchMainTab('matches')">比赛</div>
    <div id="main-tab-monitor" class="main-tab" onclick="switchMainTab('monitor')">监控</div>
    <div id="main-tab-settings" class="main-tab" onclick="switchMainTab('settings')">设置</div>
  </div>
  
  <!-- 添加底部空间，避免内容被底部导航栏遮挡 -->
  <div style="height: 40px;"></div>
</body>
</html> 