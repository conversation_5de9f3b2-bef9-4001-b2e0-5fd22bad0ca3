document.addEventListener('DOMContentLoaded', function() {
  const getSourceBtn = document.getElementById('getSourceBtn');
  const exportBtn = document.getElementById('exportBtn');
  const importBtn = document.getElementById('importBtn');
  const selectLeaguesBtn = document.getElementById('selectLeaguesBtn');
  const fileInput = document.getElementById('fileInput');
  const tableContainer = document.getElementById('tableContainer');
  const toggleMonitorBtn = document.getElementById('toggleMonitorBtn');
  const clearLogBtn = document.getElementById('clearLogBtn');
  const logContainer = document.getElementById('logContainer');
  const soundToggle = document.getElementById('soundToggle');
  const timeLimit = document.getElementById('timeLimit');
  const soundType = document.getElementById('soundType');
  const testSoundBtn = document.getElementById('testSoundBtn');
  const tab1 = document.getElementById('tab1');
  const tab2 = document.getElementById('tab2');
  const tab3 = document.getElementById('tab3');
  const tab4 = document.getElementById('tab4');
  const tabContent1 = document.getElementById('tabContent1');
  const tabContent2 = document.getElementById('tabContent2');
  const tabContent3 = document.getElementById('tabContent3');
  const tabContent4 = document.getElementById('tabContent4');

  // 套利计算器元素
  const oddsA = document.getElementById('oddsA');
  const betA = document.getElementById('betA');
  const oddsB = document.getElementById('oddsB');
  const betB = document.getElementById('betB');
  const calculateBtn = document.getElementById('calculateBtn');
  const arbitrageResult = document.getElementById('arbitrageResult');

  // 计算器元素
  const calculatorInput = document.getElementById('calculatorInput');
  const calculatorResult = document.getElementById('calculatorResult');

  let currentLeagueData = []; // 存储当前表格数据
  let monitorInterval = null; // 监控定时器
  let lastScoreData = {}; // 存储上次的比分数据
  let audioContext = null; // 音频上下文
  let soundEnabled = true; // 提示音状态

  // 初始化音频上下文
  function initAudio() {
    if (!audioContext) {
      audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
  }

  // 播放提示音
  function playNotificationSound() {
    if (!soundEnabled) return;

    try {
      initAudio();
      const selectedType = soundType.value;

      switch (selectedType) {
        case 'beep':
          playBeepSound();
          break;
        case 'ding':
          playDingSound();
          break;
        case 'chime':
          playChimeSound();
          break;
        case 'notification':
          playNotificationBeep();
          break;
        case 'alert':
          playAlertSound();
          break;
        default:
          playBeepSound();
      }
    } catch (error) {
      console.log('播放提示音失败:', error);
    }
  }

  // 哔哔声
  function playBeepSound() {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    gainNode.gain.setValueAtTime(0.8, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
  }

  // 叮咚声
  function playDingSound() {
    const oscillator1 = audioContext.createOscillator();
    const oscillator2 = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator1.connect(gainNode);
    oscillator2.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator1.frequency.setValueAtTime(900, audioContext.currentTime);
    oscillator2.frequency.setValueAtTime(700, audioContext.currentTime + 0.15);

    gainNode.gain.setValueAtTime(0.8, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    oscillator1.start(audioContext.currentTime);
    oscillator1.stop(audioContext.currentTime + 0.2);
    oscillator2.start(audioContext.currentTime + 0.15);
    oscillator2.stop(audioContext.currentTime + 0.5);
  }

  // 钟声
  function playChimeSound() {
    const frequencies = [523, 659, 784]; // C, E, G
    frequencies.forEach((freq, index) => {
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
      gainNode.gain.setValueAtTime(0.6, audioContext.currentTime + index * 0.1);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.0);

      oscillator.start(audioContext.currentTime + index * 0.1);
      oscillator.stop(audioContext.currentTime + 1.0);
    });
  }

  // 通知音
  function playNotificationBeep() {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(1100, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(900, audioContext.currentTime + 0.1);
    oscillator.frequency.setValueAtTime(1100, audioContext.currentTime + 0.2);

    gainNode.gain.setValueAtTime(0.8, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.4);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.4);
  }

  // 警报音
  function playAlertSound() {
    for (let i = 0; i < 4; i++) {
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(1400, audioContext.currentTime + i * 0.15);
      gainNode.gain.setValueAtTime(0.9, audioContext.currentTime + i * 0.15);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + i * 0.15 + 0.12);

      oscillator.start(audioContext.currentTime + i * 0.15);
      oscillator.stop(audioContext.currentTime + i * 0.15 + 0.12);
    }
  }

  console.log('页面加载完成，按钮:', getSourceBtn);

  if (!getSourceBtn) {
    console.error('找不到按钮元素');
    return;
  }

  getSourceBtn.addEventListener('click', async function() {
    console.log('按钮被点击');
    tableContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">正在解析联赛名称...</div>';

    try {
      // 获取所有标签页
      const tabs = await chrome.tabs.query({});
      console.log('所有标签页:', tabs);

      // 找到最近活动的非插件标签页
      let targetTab = null;
      for (const tab of tabs) {
        if (!tab.url.startsWith('chrome-extension://') &&
            !tab.url.startsWith('chrome://') &&
            !tab.url.startsWith('moz-extension://')) {
          targetTab = tab;
          break;
        }
      }

      if (!targetTab) {
        tableContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: red;">未找到可访问的标签页</div>';
        return;
      }

      console.log('目标标签页:', targetTab);

      // 使用消息传递获取页面源码
      const response = await chrome.tabs.sendMessage(targetTab.id, { action: 'getPageSource' });
      console.log('获取到页面源码长度:', response?.source?.length);

      if (response && response.source) {
        // 在popup中解析联赛名称
        const leagues = parseLeaguesFromPageSource(response.source);
        console.log('解析结果:', leagues);

        if (leagues.length > 0) {
          mergeLeagueData(leagues);
        } else {
          tableContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: red;">未找到联赛名称</div>';
        }
      } else {
        tableContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: red;">无法获取页面源码，请确保在即时比分网站上</div>';
      }
    } catch (error) {
      console.error('获取联赛名称时出错:', error);
      tableContainer.innerHTML = `<div style="text-align: center; padding: 20px; color: red;">获取联赛名称时出错: ${error.message}</div>`;
    }
  });

  // 导出数据
  exportBtn.addEventListener('click', function() {
    if (currentLeagueData.length === 0) {
      alert('没有数据可导出，请先解析联赛数据');
      return;
    }

    // 收集当前表格中的状态和备注数据
    const exportData = currentLeagueData.map(item => {
      const noteInput = document.querySelector(`input.note-input[data-league="${item.league}"]`);
      const statusCheckbox = document.querySelector(`input.status-checkbox[data-league="${item.league}"]`);
      return {
        league: item.league,
        status: statusCheckbox ? statusCheckbox.checked : false,
        note: noteInput ? noteInput.value : ''
      };
    });

    // 创建下载链接
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `联赛数据_${new Date().toISOString().slice(0, 10)}.json`;
    link.click();

    URL.revokeObjectURL(url);
  });

  // 导入数据
  importBtn.addEventListener('click', function() {
    fileInput.click();
  });

  fileInput.addEventListener('change', function(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const importData = JSON.parse(e.target.result);

        if (!Array.isArray(importData)) {
          alert('文件格式错误，请选择正确的JSON文件');
          return;
        }

        // 验证数据格式
        const isValidFormat = importData.every(item =>
          item.hasOwnProperty('league') &&
          item.hasOwnProperty('note') &&
          (item.hasOwnProperty('status') || true) // status字段可选
        );

        if (!isValidFormat) {
          alert('文件数据格式错误');
          return;
        }

        // 显示导入的数据并排序
        currentLeagueData = sortLeaguesByPopularity(importData);
        displayLeagueTable(currentLeagueData);

      } catch (error) {
        alert('文件解析失败：' + error.message);
      }
    };

    reader.readAsText(file);
    fileInput.value = ''; // 清空文件输入
  });

  // 选中网页中的联赛
  selectLeaguesBtn.addEventListener('click', async function() {
    if (currentLeagueData.length === 0) {
      alert('请先解析联赛数据');
      return;
    }

    // 获取所有联赛的状态信息
    const leagueStatusMap = {};
    currentLeagueData.forEach(item => {
      leagueStatusMap[item.league] = item.status || false;
    });

    try {
      // 获取当前活动标签页
      const tabs = await chrome.tabs.query({});
      let targetTab = null;

      for (const tab of tabs) {
        if (!tab.url.startsWith('chrome-extension://') &&
            !tab.url.startsWith('chrome://') &&
            !tab.url.startsWith('moz-extension://')) {
          targetTab = tab;
          break;
        }
      }

      if (!targetTab) {
        alert('未找到可访问的标签页');
        return;
      }

      // 注入脚本同步网页中的联赛复选框状态
      const results = await chrome.scripting.executeScript({
        target: { tabId: targetTab.id },
        function: syncLeagueCheckboxes,
        args: [leagueStatusMap]
      });

      if (results && results[0] && results[0].result) {
        const result = results[0].result;
        alert(`操作完成：选中 ${result.selected} 个联赛，取消选中 ${result.unselected} 个联赛`);
      } else {
        alert('未找到匹配的联赛复选框');
      }

    } catch (error) {
      console.error('选中联赛时出错:', error);
      alert('选中联赛时出错: ' + error.message);
    }
  });

  // 切换监控状态
  toggleMonitorBtn.addEventListener('click', async function() {
    if (monitorInterval) {
      // 当前正在监控，点击停止
      clearInterval(monitorInterval);
      monitorInterval = null;
      addLog('停止监控', 'info');

      // 更新按钮状态
      this.textContent = '开始监控';
      this.style.backgroundColor = '#28a745';
      this.title = '点击开始监控比赛事件';
    } else {
      // 当前未监控，点击开始
      // 初始化音频上下文
      initAudio();

      // 清空日志容器
      logContainer.innerHTML = '';

      addLog('开始监控比赛事件...', 'info');

      // 每1秒检查一次比分变化
      monitorInterval = setInterval(async () => {
        await checkScoreChanges();
      }, 1000);

      // 更新按钮状态
      this.textContent = '停止监控';
      this.style.backgroundColor = '#dc3545';
      this.title = '点击停止监控比赛事件';
    }
  });

  // 清空日志
  clearLogBtn.addEventListener('click', function() {
    logContainer.innerHTML = '<div style="color: #666;">日志已清空</div>';
  });

  // 喇叭按钮点击事件
  soundToggle.addEventListener('click', function() {
    soundEnabled = !soundEnabled;

    if (soundEnabled) {
      this.textContent = '🔊';
      this.title = '点击关闭提示音';
      // 播放一声提示音表示已开启
      playNotificationSound();
    } else {
      this.textContent = '🔇';
      this.title = '点击开启提示音';
    }
  });

  // 试听按钮事件
  testSoundBtn.addEventListener('click', function() {
    // 临时启用声音来试听
    const originalSoundEnabled = soundEnabled;
    soundEnabled = true;
    initAudio();
    playNotificationSound();
    soundEnabled = originalSoundEnabled;
  });

  // 标签页切换事件
  tab1.addEventListener('click', function() {
    switchTab(1);
  });

  tab2.addEventListener('click', function() {
    switchTab(2);
  });

  tab3.addEventListener('click', function() {
    switchTab(3);
  });

  tab4.addEventListener('click', function() {
    switchTab(4);
  });

  // 切换标签页函数
  function switchTab(tabNumber) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-button').forEach(btn => {
      btn.classList.remove('active');
      btn.style.color = '#666';
      btn.style.borderBottomColor = 'transparent';
    });

    // 隐藏所有内容
    document.querySelectorAll('.tab-content').forEach(content => {
      content.style.display = 'none';
    });

    // 激活选中的标签页
    if (tabNumber === 1) {
      tab1.classList.add('active');
      tab1.style.color = '#007bff';
      tab1.style.borderBottomColor = '#007bff';
      tabContent1.style.display = 'block';
    } else if (tabNumber === 2) {
      tab2.classList.add('active');
      tab2.style.color = '#007bff';
      tab2.style.borderBottomColor = '#007bff';
      tabContent2.style.display = 'block';
    } else if (tabNumber === 3) {
      tab3.classList.add('active');
      tab3.style.color = '#007bff';
      tab3.style.borderBottomColor = '#007bff';
      tabContent3.style.display = 'block';
    } else if (tabNumber === 4) {
      tab4.classList.add('active');
      tab4.style.color = '#007bff';
      tab4.style.borderBottomColor = '#007bff';
      tabContent4.style.display = 'block';
    }
  }

  // 套利计算器功能
  calculateBtn.addEventListener('click', function() {
    const oA = parseFloat(oddsA.value);
    const bA = parseFloat(betA.value);
    const oB = parseFloat(oddsB.value);

    if (!oA || !bA || !oB || oA < 1 || oB < 1 || bA <= 0) {
      arbitrageResult.innerHTML = `
        <div style="text-align: center; color: #dc3545; padding: 20px;">
          <strong>⚠️ 输入错误</strong><br>
          请输入有效的赔率（≥1.00）和A平台投注金额（>0）
        </div>
      `;
      return;
    }

    // 计算对冲套利
    const result = calculateHedgeArbitrage(oA, bA, oB);
    displayHedgeResult(result);
  });

  // 对冲套利计算函数
  function calculateHedgeArbitrage(oddsA, betA, oddsB) {
    // 计算B平台需要投注的金额（对冲A平台的风险）
    const betBAmount = (betA * oddsA) / oddsB;

    // 更新B平台投注金额显示
    betB.value = betBAmount.toFixed(2);

    // 计算总投注金额
    const totalBet = betA + betBAmount;

    // 计算两种结果的盈亏
    // 情况1：A平台赢，B平台输
    const scenarioA_win = (betA * oddsA) - totalBet;  // A平台赢得的钱 - 总投注

    // 情况2：A平台输，B平台赢
    const scenarioB_win = (betBAmount * oddsB) - totalBet;  // B平台赢得的钱 - 总投注

    return {
      betA: betA,
      betB: betBAmount,
      totalBet: totalBet,
      oddsA: oddsA,
      oddsB: oddsB,
      scenarioA: {
        description: 'A平台赢，B平台输',
        profit: scenarioA_win,
        winAmount: betA * oddsA,
        loseAmount: betBAmount
      },
      scenarioB: {
        description: 'A平台输，B平台赢',
        profit: scenarioB_win,
        winAmount: betBAmount * oddsB,
        loseAmount: betA
      },
      isProfit: scenarioA_win > 0 && scenarioB_win > 0,
      avgProfit: (scenarioA_win + scenarioB_win) / 2
    };
  }

  // 显示对冲套利结果
  function displayHedgeResult(result) {
    let resultHTML = '';

    // 判断是否盈利
    if (result.isProfit) {
      resultHTML = `
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin-bottom: 15px;">
          <h5 style="color: #155724; margin: 0 0 10px 0; font-size: 14px;">✅ 套利成功！</h5>
          <div style="color: #155724;">
            <strong>无论输赢都能盈利！</strong><br>
            <span style="font-size: 11px;">平均盈利：${result.avgProfit.toFixed(2)} 元</span>
          </div>
        </div>
      `;
    } else {
      resultHTML = `
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin-bottom: 15px;">
          <h5 style="color: #856404; margin: 0 0 10px 0; font-size: 14px;">⚠️ 无法完全套利</h5>
          <div style="color: #856404;">
            <strong>存在亏损风险</strong><br>
            <span style="font-size: 11px;">建议重新评估赔率或调整投注金额</span>
          </div>
        </div>
      `;
    }

    resultHTML += `
      <div style="margin-bottom: 15px;">
        <h5 style="margin: 0 0 10px 0; font-size: 13px; color: #333;">💰 投注分配</h5>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
          <div style="background-color: #e3f2fd; border: 1px solid #bbdefb; padding: 12px; border-radius: 4px;">
            <div style="font-weight: bold; color: #1976d2; font-size: 12px; margin-bottom: 5px;">🅰️ A平台</div>
            <div style="font-size: 11px; color: #333;">
              赔率：${result.oddsA}<br>
              投注：${result.betA.toFixed(2)} 元
            </div>
          </div>
          <div style="background-color: #e8f5e8; border: 1px solid #c8e6c8; padding: 12px; border-radius: 4px;">
            <div style="font-weight: bold; color: #388e3c; font-size: 12px; margin-bottom: 5px;">🅱️ B平台</div>
            <div style="font-size: 11px; color: #333;">
              赔率：${result.oddsB}<br>
              投注：${result.betB.toFixed(2)} 元
            </div>
          </div>
        </div>
        <div style="text-align: center; margin-top: 10px; padding: 8px; background-color: #f8f9fa; border-radius: 4px;">
          <strong style="color: #333; font-size: 12px;">总投注：${result.totalBet.toFixed(2)} 元</strong>
        </div>
      </div>

      <div style="margin-bottom: 15px;">
        <h5 style="margin: 0 0 10px 0; font-size: 13px; color: #333;">📊 盈亏分析</h5>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
          <div style="background-color: #fff; border: 1px solid #ddd; padding: 12px; border-radius: 4px;">
            <div style="font-weight: bold; color: #007bff; font-size: 11px; margin-bottom: 8px;">${result.scenarioA.description}</div>
            <div style="font-size: 10px; color: #666; margin-bottom: 5px;">
              A平台赢：+${result.scenarioA.winAmount.toFixed(2)} 元<br>
              B平台输：-${result.scenarioA.loseAmount.toFixed(2)} 元
            </div>
            <div style="font-weight: bold; color: ${result.scenarioA.profit >= 0 ? '#28a745' : '#dc3545'};">
              净收益：${result.scenarioA.profit >= 0 ? '+' : ''}${result.scenarioA.profit.toFixed(2)} 元
            </div>
          </div>
          <div style="background-color: #fff; border: 1px solid #ddd; padding: 12px; border-radius: 4px;">
            <div style="font-weight: bold; color: #28a745; font-size: 11px; margin-bottom: 8px;">${result.scenarioB.description}</div>
            <div style="font-size: 10px; color: #666; margin-bottom: 5px;">
              B平台赢：+${result.scenarioB.winAmount.toFixed(2)} 元<br>
              A平台输：-${result.scenarioB.loseAmount.toFixed(2)} 元
            </div>
            <div style="font-weight: bold; color: ${result.scenarioB.profit >= 0 ? '#28a745' : '#dc3545'};">
              净收益：${result.scenarioB.profit >= 0 ? '+' : ''}${result.scenarioB.profit.toFixed(2)} 元
            </div>
          </div>
        </div>
      </div>

      <div style="background-color: #e9ecef; padding: 10px; border-radius: 4px; font-size: 11px; color: #495057;">
        <strong>💡 使用说明：</strong><br>
        • A平台投注您指定的金额和赔率<br>
        • B平台投注计算出的金额进行对冲<br>
        • 两个结果显示不同情况下的盈亏
      </div>
    `;

    arbitrageResult.innerHTML = resultHTML;
  }

  // 计算器功能
  calculatorInput.addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
      calculateExpression();
    }
  });

  // 计算数学表达式
  function calculateExpression() {
    const expression = calculatorInput.value.trim();

    if (!expression) {
      displayCalculatorResult('请输入计算表达式', 'error');
      return;
    }

    try {
      // 安全的数学表达式计算
      const result = evaluateMathExpression(expression);
      displayCalculatorResult(expression, 'success', result);

      // 清空输入框
      calculatorInput.value = '';
    } catch (error) {
      displayCalculatorResult(expression, 'error', error.message);
    }
  }

  // 安全的数学表达式求值 - 使用递归下降解析器
  function evaluateMathExpression(expr) {
    // 预处理：替换常数和函数
    let expression = expr.replace(/\s+/g, ''); // 移除空格
    expression = expression.replace(/PI/g, Math.PI.toString());
    expression = expression.replace(/E/g, Math.E.toString());

    // 创建解析器
    const parser = new MathExpressionParser(expression);
    return parser.parse();
  }

  // 数学表达式解析器类
  class MathExpressionParser {
    constructor(expression) {
      this.expression = expression;
      this.position = 0;
      this.length = expression.length;
    }

    // 获取当前字符
    peek() {
      return this.position < this.length ? this.expression[this.position] : null;
    }

    // 消费当前字符
    consume() {
      return this.position < this.length ? this.expression[this.position++] : null;
    }

    // 跳过空格
    skipWhitespace() {
      while (this.peek() === ' ' || this.peek() === '\t') {
        this.consume();
      }
    }

    // 主解析函数
    parse() {
      const result = this.parseExpression();
      if (this.position < this.length) {
        throw new Error('表达式解析错误：存在多余字符');
      }
      return result;
    }

    // 解析表达式（处理加法和减法）
    parseExpression() {
      let result = this.parseTerm();

      while (this.peek() === '+' || this.peek() === '-') {
        const operator = this.consume();
        const right = this.parseTerm();

        if (operator === '+') {
          result += right;
        } else {
          result -= right;
        }
      }

      return result;
    }

    // 解析项（处理乘法和除法）
    parseTerm() {
      let result = this.parseFactor();

      while (this.peek() === '*' || this.peek() === '/') {
        const operator = this.consume();
        const right = this.parseFactor();

        if (operator === '*') {
          result *= right;
        } else {
          if (right === 0) {
            throw new Error('除零错误');
          }
          result /= right;
        }
      }

      return result;
    }

    // 解析因子（处理幂运算、函数、括号、数字）
    parseFactor() {
      let result = this.parseBase();

      // 处理幂运算
      if (this.peek() === '*' && this.position + 1 < this.length && this.expression[this.position + 1] === '*') {
        this.consume(); // 消费第一个*
        this.consume(); // 消费第二个*
        const exponent = this.parseFactor(); // 右结合
        result = Math.pow(result, exponent);
      }

      return result;
    }

    // 解析基础元素（数字、函数、括号）
    parseBase() {
      this.skipWhitespace();

      // 处理负号
      if (this.peek() === '-') {
        this.consume();
        return -this.parseBase();
      }

      // 处理正号
      if (this.peek() === '+') {
        this.consume();
        return this.parseBase();
      }

      // 处理括号
      if (this.peek() === '(') {
        this.consume();
        const result = this.parseExpression();
        if (this.peek() !== ')') {
          throw new Error('缺少右括号');
        }
        this.consume();
        return result;
      }

      // 处理函数
      if (this.isLetter(this.peek())) {
        return this.parseFunction();
      }

      // 处理数字
      if (this.isDigit(this.peek()) || this.peek() === '.') {
        return this.parseNumber();
      }

      throw new Error('无效的表达式');
    }

    // 解析函数
    parseFunction() {
      let funcName = '';
      while (this.isLetter(this.peek())) {
        funcName += this.consume();
      }

      if (this.peek() !== '(') {
        throw new Error(`函数 ${funcName} 缺少左括号`);
      }
      this.consume(); // 消费 (

      const args = [];
      if (this.peek() !== ')') {
        args.push(this.parseExpression());
        while (this.peek() === ',') {
          this.consume(); // 消费 ,
          args.push(this.parseExpression());
        }
      }

      if (this.peek() !== ')') {
        throw new Error(`函数 ${funcName} 缺少右括号`);
      }
      this.consume(); // 消费 )

      return this.callFunction(funcName, args);
    }

    // 调用数学函数
    callFunction(name, args) {
      switch (name.toLowerCase()) {
        case 'sqrt':
          if (args.length !== 1) throw new Error('sqrt 函数需要1个参数');
          return Math.sqrt(args[0]);
        case 'sin':
          if (args.length !== 1) throw new Error('sin 函数需要1个参数');
          return Math.sin(args[0]);
        case 'cos':
          if (args.length !== 1) throw new Error('cos 函数需要1个参数');
          return Math.cos(args[0]);
        case 'tan':
          if (args.length !== 1) throw new Error('tan 函数需要1个参数');
          return Math.tan(args[0]);
        case 'log':
          if (args.length !== 1) throw new Error('log 函数需要1个参数');
          return Math.log(args[0]);
        case 'abs':
          if (args.length !== 1) throw new Error('abs 函数需要1个参数');
          return Math.abs(args[0]);
        case 'pow':
          if (args.length !== 2) throw new Error('pow 函数需要2个参数');
          return Math.pow(args[0], args[1]);
        case 'floor':
          if (args.length !== 1) throw new Error('floor 函数需要1个参数');
          return Math.floor(args[0]);
        case 'ceil':
          if (args.length !== 1) throw new Error('ceil 函数需要1个参数');
          return Math.ceil(args[0]);
        case 'round':
          if (args.length !== 1) throw new Error('round 函数需要1个参数');
          return Math.round(args[0]);
        default:
          throw new Error(`未知函数: ${name}`);
      }
    }

    // 解析数字
    parseNumber() {
      let numStr = '';
      let hasDot = false;

      while (this.isDigit(this.peek()) || (this.peek() === '.' && !hasDot)) {
        if (this.peek() === '.') {
          hasDot = true;
        }
        numStr += this.consume();
      }

      const result = parseFloat(numStr);
      if (isNaN(result)) {
        throw new Error('无效的数字格式');
      }

      return result;
    }

    // 检查是否为字母
    isLetter(char) {
      return char && /[a-zA-Z]/.test(char);
    }

    // 检查是否为数字
    isDigit(char) {
      return char && /[0-9]/.test(char);
    }
  }

  // 显示计算器结果
  function displayCalculatorResult(expression, type, result = null) {
    const timestamp = new Date().toLocaleTimeString();

    let resultHTML = '';

    if (type === 'success') {
      resultHTML = `
        <div style="border-bottom: 1px solid #eee; padding: 10px 0; margin-bottom: 10px;">
          <div style="color: #666; font-size: 11px; margin-bottom: 5px;">[${timestamp}]</div>
          <div style="font-family: monospace; color: #333; margin-bottom: 5px;">
            <strong>输入：</strong>${expression}
          </div>
          <div style="font-family: monospace; color: #28a745; font-size: 16px;">
            <strong>结果：</strong>${result}
          </div>
        </div>
      `;
    } else if (type === 'error') {
      resultHTML = `
        <div style="border-bottom: 1px solid #eee; padding: 10px 0; margin-bottom: 10px;">
          <div style="color: #666; font-size: 11px; margin-bottom: 5px;">[${timestamp}]</div>
          <div style="font-family: monospace; color: #333; margin-bottom: 5px;">
            <strong>输入：</strong>${expression}
          </div>
          <div style="color: #dc3545;">
            <strong>错误：</strong>${result || '计算失败'}
          </div>
        </div>
      `;
    }

    // 将新结果添加到顶部
    const currentContent = calculatorResult.innerHTML;
    if (currentContent.includes('输入数学表达式并按回车键查看结果')) {
      calculatorResult.innerHTML = resultHTML;
    } else {
      calculatorResult.innerHTML = resultHTML + currentContent;
    }
  }

  // 合并联赛数据（只添加新的联赛）
  function mergeLeagueData(newLeagues) {
    if (currentLeagueData.length === 0) {
      // 如果当前没有数据，直接使用新数据并排序
      currentLeagueData = sortLeaguesByPopularity(newLeagues);
      displayLeagueTable(currentLeagueData);
      return;
    }

    // 获取当前已存在的联赛名称
    const existingLeagues = new Set(currentLeagueData.map(item => item.league));

    // 筛选出新的联赛
    const newUniqueLeagues = newLeagues.filter(item => !existingLeagues.has(item.league));

    if (newUniqueLeagues.length === 0) {
      // 重新显示当前表格，确保不会清空
      displayLeagueTable(currentLeagueData);
      alert('没有发现新的联赛，所有联赛都已存在');
      return;
    }

    // 合并数据
    currentLeagueData = [...currentLeagueData, ...newUniqueLeagues];

    // 重新排序
    currentLeagueData = sortLeaguesByPopularity(currentLeagueData);

    displayLeagueTable(currentLeagueData);

    alert(`成功添加 ${newUniqueLeagues.length} 个新联赛`);
  }

  // 按热门联赛排序
  function sortLeaguesByPopularity(leagueArray) {
    const hotLeagues = [
      '英超', '西甲', '德甲', '意甲', '法甲', '中超',
      '欧冠', '欧联', '世界杯', '欧洲杯',
      '巴西甲', '阿甲', '荷甲', '葡超', '俄超',
      '土超', '比甲', '苏超', '奥甲', '瑞士超',
      '挪超', '瑞典超', '丹超', '芬超', '波兰甲',
      '捷甲', '克甲', '塞超', '保超', '罗甲',
      '乌超', '希超', '以超', '日职', '韩职',
      '澳超', '美职', '墨超'
    ];

    return leagueArray.sort((a, b) => {
      const leagueA = a.league || a; // 兼容不同数据格式
      const leagueB = b.league || b;
      const statusA = a.status || false;
      const statusB = b.status || false;

      // 第一优先级：状态选中的排在前面
      if (statusA && !statusB) {
        return -1;
      }
      if (!statusA && statusB) {
        return 1;
      }

      // 第二优先级：热门联赛排序
      const indexA = hotLeagues.indexOf(leagueA);
      const indexB = hotLeagues.indexOf(leagueB);

      // 如果都在热门列表中，按热门顺序排序
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }

      // 如果只有一个在热门列表中，热门的排在前面
      if (indexA !== -1 && indexB === -1) {
        return -1;
      }
      if (indexA === -1 && indexB !== -1) {
        return 1;
      }

      // 第三优先级：如果都不在热门列表中，按字母顺序排序
      return leagueA.localeCompare(leagueB, 'zh-CN');
    });
  }

  // 显示联赛表格
  function displayLeagueTable(leagueData) {
    if (!leagueData || leagueData.length === 0) {
      tableContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">未找到联赛数据</div>';
      return;
    }

    let tableHTML = `
      <table>
        <thead>
          <tr>
            <th><input type="checkbox" id="selectAllCheckbox" title="全选/取消全选"> 状态</th>
            <th>联赛名称</th>
            <th>备注</th>
          </tr>
        </thead>
        <tbody>
    `;

    leagueData.forEach((item) => {
      const noteValue = item.note || '';
      const statusChecked = item.status || false;
      tableHTML += `
        <tr>
          <td><input type="checkbox" class="status-checkbox" data-league="${item.league}" ${statusChecked ? 'checked' : ''}></td>
          <td>${item.league}</td>
          <td><input type="text" class="note-input" data-league="${item.league}" value="${noteValue}" placeholder="添加备注..." style="width: 100%; border: none; padding: 4px; background: transparent;"></td>
        </tr>
      `;
    });

    tableHTML += `
        </tbody>
      </table>
    `;

    tableContainer.innerHTML = tableHTML;

    // 为全选复选框添加事件监听器
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;

        // 更新所有联赛的状态
        currentLeagueData.forEach(item => {
          item.status = isChecked;
        });

        // 重新排序并显示
        currentLeagueData = sortLeaguesByPopularity(currentLeagueData);
        displayLeagueTable(currentLeagueData);
      });
    }

    // 为复选框添加变化监听器，实现实时排序
    const checkboxes = tableContainer.querySelectorAll('.status-checkbox');
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        // 更新当前数据的状态
        const league = this.getAttribute('data-league');
        const item = currentLeagueData.find(data => data.league === league);
        if (item) {
          item.status = this.checked;
        }

        // 重新排序并显示
        currentLeagueData = sortLeaguesByPopularity(currentLeagueData);
        displayLeagueTable(currentLeagueData);

        // 更新全选复选框状态
        updateSelectAllCheckbox();
      });
    });

    // 初始化全选复选框状态
    updateSelectAllCheckbox();
  }

  // 更新全选复选框状态
  function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (!selectAllCheckbox || currentLeagueData.length === 0) return;

    const checkedCount = currentLeagueData.filter(item => item.status === true).length;
    const totalCount = currentLeagueData.length;

    if (checkedCount === 0) {
      // 没有选中任何项
      selectAllCheckbox.checked = false;
      selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === totalCount) {
      // 全部选中
      selectAllCheckbox.checked = true;
      selectAllCheckbox.indeterminate = false;
    } else {
      // 部分选中
      selectAllCheckbox.checked = false;
      selectAllCheckbox.indeterminate = true;
    }
  }

  // 检查比分变化
  async function checkScoreChanges() {
    try {
      const tabs = await chrome.tabs.query({});
      let targetTab = null;

      for (const tab of tabs) {
        if (!tab.url.startsWith('chrome-extension://') &&
            !tab.url.startsWith('chrome://') &&
            !tab.url.startsWith('moz-extension://')) {
          targetTab = tab;
          break;
        }
      }

      if (!targetTab) return;

      const results = await chrome.scripting.executeScript({
        target: { tabId: targetTab.id },
        function: getMatchScores
      });

      if (results && results[0] && results[0].result) {
        const currentMatches = results[0].result;

        // 检查比分和红牌变化
        Object.keys(currentMatches).forEach(matchId => {
          const currentMatch = currentMatches[matchId];
          const lastMatch = lastScoreData[matchId];

          const leagueName = currentMatch.league;

          // 只监控选中的联赛
          const selectedLeague = currentLeagueData.find(item =>
            item.league === leagueName && item.status === true
          );

          if (selectedLeague && lastMatch) {
            // 检查是否在时间限制内
            const timeLimitMinutes = parseInt(timeLimit.value) || 80;
            const shouldNotify = isWithinTimeLimit(currentMatch.matchStatus, timeLimitMinutes);

            // 检查比分变化
            if (lastMatch.score !== currentMatch.score) {
              const statusInfo = currentMatch.matchStatus ? ` [${currentMatch.matchStatus}]` : '';

              // 判断是进球还是取消进球
              const lastGoals = getGoalCount(lastMatch.score);
              const currentGoals = getGoalCount(currentMatch.score);

              if (currentGoals > lastGoals) {
                // 进球
                addLog(`⚽ ${leagueName}: ${currentMatch.team1} vs ${currentMatch.team2} - ${currentMatch.score}${statusInfo}`, 'goal');
                if (shouldNotify) playNotificationSound();
              } else if (currentGoals < lastGoals) {
                // 取消进球
                addLog(`❌ ${leagueName}: ${currentMatch.team1} vs ${currentMatch.team2} - 取消进球 ${currentMatch.score}${statusInfo}`, 'goal-cancel');
                if (shouldNotify) playNotificationSound();
              }
            }

            // 检查红牌变化
            if (lastMatch.redCards !== currentMatch.redCards) {
              const redCardDiff = currentMatch.redCards - lastMatch.redCards;
              if (redCardDiff > 0) {
                const statusInfo = currentMatch.matchStatus ? ` [${currentMatch.matchStatus}]` : '';
                addLog(`🟥 ${leagueName}: ${currentMatch.team1} vs ${currentMatch.team2} - 红牌！${statusInfo}`, 'redcard');
                if (shouldNotify) playNotificationSound();
              }
            }
          }
        });

        // 更新比赛数据
        lastScoreData = currentMatches;
      }
    } catch (error) {
      console.error('检查比分时出错:', error);
    }
  }

  // 获取比分中的总进球数
  function getGoalCount(score) {
    if (!score || score === '-') return 0;
    const parts = score.split('-');
    if (parts.length !== 2) return 0;
    const home = parseInt(parts[0]) || 0;
    const away = parseInt(parts[1]) || 0;
    return home + away;
  }

  // 检查是否在时间限制内
  function isWithinTimeLimit(matchStatus, timeLimitMinutes) {
    if (!matchStatus) return true;

    // 如果比赛未开始或已结束，不提示
    if (matchStatus.includes('未开') || matchStatus.includes('完') || matchStatus.includes('结束')) {
      return false;
    }

    // 提取比赛进行的分钟数
    const timeMatch = matchStatus.match(/(\d+)/);
    if (timeMatch) {
      const currentMinute = parseInt(timeMatch[1]);
      // 如果比赛进行时间超过设定的限制分钟数，不提示
      return currentMinute <= timeLimitMinutes;
    }

    return true; // 其他状态默认提示
  }

  // 添加日志
  function addLog(message, type = 'info') {
    const now = new Date();
    const timeStr = now.toLocaleTimeString();

    let color = '#333';
    let fontWeight = 'normal';

    if (type === 'goal') {
      color = '#28a745';
      fontWeight = 'bold';
    }
    if (type === 'goal-cancel') {
      color = '#ff6b35';
      fontWeight = 'bold';
    }
    if (type === 'redcard') {
      color = '#dc3545';
      fontWeight = 'bold';
    }
    if (type === 'info') color = '#007bff';
    if (type === 'error') color = '#dc3545';

    const logEntry = document.createElement('div');
    logEntry.style.color = color;
    logEntry.style.fontWeight = fontWeight;
    logEntry.style.marginBottom = '3px';
    logEntry.innerHTML = `[${timeStr}] ${message}`;

    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
  }
});

// 在popup中解析页面源码中的联赛名称
function parseLeaguesFromPageSource(pageSource) {
  console.log('=== 开始解析页面源码 ===');
  console.log('页面源码长度:', pageSource.length);

  const leagueSet = new Set();

  try {
    // 方法1: 使用正则表达式解析tr1_行中的联赛名称
    console.log('--- 方法1: 正则表达式解析 ---');

    // 匹配tr1_行的正则表达式
    const trPattern = /<tr[^>]*id="tr1_\d+"[^>]*>(.*?)<\/tr>/gs;
    let trMatch;
    let rowCount = 0;

    while ((trMatch = trPattern.exec(pageSource)) !== null && rowCount < 500) {
      const trContent = trMatch[1];

      // 在tr内容中查找带bgcolor的td中的span
      const leaguePattern = /<td[^>]*bgcolor="[^"]*"[^>]*><span>([^<]+)<\/span><\/td>/;
      const leagueMatch = leaguePattern.exec(trContent);

      if (leagueMatch) {
        let leagueName = leagueMatch[1].trim();
        if (leagueName && leagueName.length > 0) {
          console.log(`  行${rowCount}: "${leagueName}"`);
          leagueSet.add(leagueName);
        }
      }
      rowCount++;
    }

    console.log(`处理了 ${rowCount} 行数据`);

    // 方法2: 如果方法1没找到，使用更广泛的匹配
    if (leagueSet.size === 0) {
      console.log('--- 方法2: 广泛匹配 ---');

      const bgTdPattern = /<td[^>]*bgcolor="[^"]*"[^>]*><span>([^<]+)<\/span><\/td>/gs;
      let bgMatch;
      let bgCount = 0;

      while ((bgMatch = bgTdPattern.exec(pageSource)) !== null && bgCount < 200) {
        let text = bgMatch[1].trim();
        if (text && text.length > 0 && text.length < 30 &&
            !text.match(/\d+:\d+/) && !text.match(/\d+-\d+/) &&
            !text.includes('vs') && !text.match(/^\d+$/) &&
            !text.includes('分析') && !text.includes('亚') &&
            !text.includes('大') && !text.includes('欧')) {
          console.log(`  匹配${bgCount}: "${text}"`);
          leagueSet.add(text);
        }
        bgCount++;
      }

      console.log(`广泛匹配处理了 ${bgCount} 个元素`);
    }

    console.log('=== 解析完成 ===');
    console.log('找到联赛数量:', leagueSet.size);
    console.log('联赛列表:', Array.from(leagueSet));

  } catch (error) {
    console.error('解析页面源码时出错:', error);
  }

  return Array.from(leagueSet).map(league => ({ league }));
}

// 从页面源码中解析联赛名称（旧版本，保留作为备用）
function parseLeaguesFromSource() {
  console.log('=== 开始解析联赛名称 ===');
  console.log('当前页面URL:', window.location.href);
  console.log('页面标题:', document.title);

  const leagueSet = new Set(); // 用于去重

  try {
    // 方法1: 根据实际HTML结构查找联赛名称
    console.log('--- 方法1: 查找比赛行中的联赛名称 ---');

    // 查找所有id以tr1_开头的比赛行
    const matchRows = document.querySelectorAll('tr[id^="tr1_"]');
    console.log('找到比赛行数量:', matchRows.length);

    matchRows.forEach((row, index) => {
      // 在每行中查找第二个td（联赛名称通常在第二列，第一列是复选框）
      const cells = row.querySelectorAll('td');
      if (cells.length >= 2) {
        const leagueCell = cells[1]; // 第二个td通常是联赛名称

        // 检查是否有bgcolor属性（联赛名称的td通常有背景色）
        if (leagueCell.hasAttribute('bgcolor')) {
          const span = leagueCell.querySelector('span');
          if (span) {
            let leagueName = span.textContent || span.innerText;
            if (leagueName) {
              leagueName = leagueName.trim();
              if (leagueName.length > 0) {
                console.log(`  行${index}: "${leagueName}"`);
                leagueSet.add(leagueName);
              }
            }
          }
        }
      }
    });

    // 方法2: 如果方法1没找到，尝试更广泛的查找
    if (leagueSet.size === 0) {
      console.log('--- 方法2: 广泛查找带背景色的td中的span ---');

      const bgTds = document.querySelectorAll('td[bgcolor]');
      console.log('找到带背景色的td数量:', bgTds.length);

      bgTds.forEach((td, index) => {
        const span = td.querySelector('span');
        if (span) {
          let text = span.textContent || span.innerText;
          if (text) {
            text = text.trim();
            // 过滤掉明显不是联赛名称的内容
            if (text.length > 0 &&
                text.length < 30 &&
                !text.match(/\d+:\d+/) && // 不包含时间格式
                !text.match(/\d+-\d+/) && // 不包含比分格式
                !text.includes('vs') &&
                !text.match(/^\d+$/) && // 不是纯数字
                !text.includes('分析') &&
                !text.includes('亚') &&
                !text.includes('大') &&
                !text.includes('欧')) {
              console.log(`  td${index}: "${text}"`);
              leagueSet.add(text);
            }
          }
        }
      });
    }

    // 方法3: 正则表达式解析（针对实际HTML结构）
    if (leagueSet.size === 0) {
      console.log('--- 方法3: 正则表达式解析 ---');

      const pageSource = document.documentElement.outerHTML;
      console.log('页面源码长度:', pageSource.length);

      // 专门针对tr1_行结构的正则表达式
      const trPattern = /<tr[^>]*id="tr1_\d+"[^>]*>.*?<td[^>]*bgcolor="[^"]*"[^>]*><span>([^<]+)<\/span><\/td>/gs;
      let match;
      let matchCount = 0;

      while ((match = trPattern.exec(pageSource)) !== null && matchCount < 200) {
        let leagueName = match[1].trim();
        if (leagueName && leagueName.length > 0) {
          console.log(`  正则匹配${matchCount}: "${leagueName}"`);
          leagueSet.add(leagueName);
        }
        matchCount++;
      }
      console.log(`正则表达式总匹配数: ${matchCount}`);
    }

    // 方法4: 查找所有span中可能的联赛名称
    if (leagueSet.size === 0) {
      console.log('--- 方法4: 查找所有span元素 ---');

      const allSpans = document.querySelectorAll('span');
      console.log('找到span元素数量:', allSpans.length);

      allSpans.forEach((span, index) => {
        // 检查span的父元素是否是带背景色的td
        const parentTd = span.closest('td[bgcolor]');
        if (parentTd) {
          let text = span.textContent || span.innerText;
          if (text) {
            text = text.trim();
            if (text.length > 0 &&
                text.length < 30 &&
                !text.match(/\d+:\d+/) &&
                !text.match(/\d+-\d+/) &&
                !text.includes('vs') &&
                !text.match(/^\d+$/) &&
                !text.includes('分析')) {
              console.log(`  span${index}: "${text}"`);
              leagueSet.add(text);
            }
          }
        }
      });
    }

    console.log('=== 解析完成 ===');
    console.log('最终找到联赛数量:', leagueSet.size);
    console.log('联赛列表:', Array.from(leagueSet));

  } catch (error) {
    console.error('解析联赛时出错:', error);
  }

  // 转换为数组并返回
  const result = Array.from(leagueSet).map(league => ({ league }));
  console.log('返回结果:', result);
  return result;
}

// 同步网页中联赛复选框的状态
function syncLeagueCheckboxes(leagueStatusMap) {
  let selectedCount = 0;
  let unselectedCount = 0;

  // 查找所有比赛行
  const matchRows = document.querySelectorAll('tr[id^="tr1_"]');

  matchRows.forEach(row => {
    // 查找联赛名称
    const leagueTd = row.querySelector('td[bgcolor] span');
    if (leagueTd) {
      const leagueName = leagueTd.textContent.trim();

      // 如果联赛在状态映射中
      if (leagueStatusMap.hasOwnProperty(leagueName)) {
        const shouldBeChecked = leagueStatusMap[leagueName];

        // 查找对应的复选框
        const checkbox = row.querySelector('input[type="checkbox"].inp');
        if (checkbox) {
          if (shouldBeChecked && !checkbox.checked) {
            // 需要选中但当前未选中
            checkbox.checked = true;
            selectedCount++;
          } else if (!shouldBeChecked && checkbox.checked) {
            // 需要取消选中但当前已选中
            checkbox.checked = false;
            unselectedCount++;
          }
        }
      }
    }
  });

  return {
    selected: selectedCount,
    unselected: unselectedCount
  };
}

// 获取当前所有比赛的比分和红牌信息
function getMatchScores() {
  const matchData = {};

  // 查找所有比赛行
  const matchRows = document.querySelectorAll('tr[id^="tr1_"]');

  matchRows.forEach(row => {
    const matchId = row.id.replace('tr1_', '');

    // 获取联赛名称
    const leagueTd = row.querySelector('td[bgcolor] span');
    if (!leagueTd) return;

    const leagueName = leagueTd.textContent.trim();

    // 获取球队名称
    const team1Link = row.querySelector('a[id^="team1_"]');
    const team2Link = row.querySelector('a[id^="team2_"]');
    if (!team1Link || !team2Link) return;

    const team1 = team1Link.textContent.trim();
    const team2 = team2Link.textContent.trim();

    // 获取比分
    let score = '0-0';
    const scoreTd = row.querySelector('td.td_score');
    if (scoreTd) {
      const scoreText = scoreTd.textContent.trim();
      if (scoreText && scoreText !== '-') {
        score = scoreText;
      }
    }

    // 获取比赛状态
    let matchStatus = '';
    const statusTd = row.querySelector('td.td_status');
    if (statusTd) {
      matchStatus = statusTd.textContent.trim();
    }

    // 获取红牌数量
    let redCards = 0;
    const redCardElements = row.querySelectorAll('img[src*="red"], .red-card, [title*="红牌"], [alt*="红牌"]');
    redCards = redCardElements.length;

    // 也可以通过文本内容查找红牌信息
    const allText = row.textContent;
    const redCardMatches = allText.match(/红牌|Red Card|🟥/gi);
    if (redCardMatches) {
      redCards = Math.max(redCards, redCardMatches.length);
    }

    matchData[matchId] = {
      league: leagueName,
      team1: team1,
      team2: team2,
      score: score,
      redCards: redCards,
      matchStatus: matchStatus
    };
  });

  return matchData;
}


